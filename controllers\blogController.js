const { Blog, User } = require('../models');
const path = require('path');
const fs = require('fs');
const multer = require('multer');

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'public/uploads/blog');
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    // Accept only images
    const filetypes = /jpeg|jpg|png|gif/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    
    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('Only image files are allowed!'));
  }
});

// Middleware for handling file uploads
exports.uploadImage = upload.single('image');

// Get all blog posts
exports.getAllBlogPosts = async (req, res) => {
  try {
    const blogs = await Blog.find({ published: true })
      .sort({ createdAt: -1 })
      .populate('author', 'name');
    
    res.render('pages/blog/index', {
      title: 'Science Club - Blog',
      blogs
    });
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching blog posts.' 
    });
  }
};

// Get a single blog post by slug
exports.getBlogPostBySlug = async (req, res) => {
  try {
    const blog = await Blog.findOne({ slug: req.params.slug, published: true })
      .populate('author', 'name');
    
    if (!blog) {
      return res.status(404).render('pages/404', { 
        title: '404 - Blog Post Not Found' 
      });
    }
    
    // Get related blog posts (same category)
    const relatedBlogs = await Blog.find({
      _id: { $ne: blog._id },
      categories: { $in: blog.categories },
      published: true
    })
      .limit(3)
      .populate('author', 'name');
    
    res.render('pages/blog/show', {
      title: `Science Club - ${blog.title}`,
      blog,
      relatedBlogs
    });
  } catch (error) {
    console.error('Error fetching blog post:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching the blog post.' 
    });
  }
};

// Admin: Get all blog posts (including unpublished)
exports.getAdminBlogPosts = async (req, res) => {
  try {
    const blogs = await Blog.find()
      .sort({ createdAt: -1 })
      .populate('author', 'name');
    
    res.render('admin/blog/index', {
      title: 'Admin - Blog Posts',
      blogs
    });
  } catch (error) {
    console.error('Error fetching admin blog posts:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching blog posts.' 
    });
  }
};

// Admin: Get form to create a new blog post
exports.getCreateBlogForm = async (req, res) => {
  try {
    const authors = await User.find().select('name');
    
    res.render('admin/blog/create', {
      title: 'Admin - Create New Blog Post',
      authors
    });
  } catch (error) {
    console.error('Error loading create blog form:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while loading the create blog form.' 
    });
  }
};

// Admin: Create a new blog post
exports.createBlogPost = async (req, res) => {
  try {
    const { title, content, shortDescription, categories, tags, author, featured, published } = req.body;
    
    // Process categories and tags (convert comma-separated string to array)
    const categoriesArray = categories ? categories.split(',').map(category => category.trim()) : [];
    const tagsArray = tags ? tags.split(',').map(tag => tag.trim()) : [];
    
    // Create new blog post
    const newBlog = new Blog({
      title,
      content,
      shortDescription,
      categories: categoriesArray,
      tags: tagsArray,
      author,
      featured: featured === 'on',
      published: published === 'on',
      image: req.file ? `/uploads/blog/${req.file.filename}` : null
    });
    
    await newBlog.save();
    
    res.redirect('/admin/blog');
  } catch (error) {
    console.error('Error creating blog post:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while creating the blog post.' 
    });
  }
};

// Admin: Get form to edit a blog post
exports.getEditBlogForm = async (req, res) => {
  try {
    const blog = await Blog.findById(req.params.id);
    const authors = await User.find().select('name');
    
    if (!blog) {
      return res.status(404).render('pages/404', { 
        title: '404 - Blog Post Not Found' 
      });
    }
    
    res.render('admin/blog/edit', {
      title: `Admin - Edit Blog Post: ${blog.title}`,
      blog,
      authors
    });
  } catch (error) {
    console.error('Error fetching blog post for edit:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching the blog post for editing.' 
    });
  }
};

// Admin: Update a blog post
exports.updateBlogPost = async (req, res) => {
  try {
    const { title, content, shortDescription, categories, tags, author, featured, published } = req.body;
    
    // Process categories and tags (convert comma-separated string to array)
    const categoriesArray = categories ? categories.split(',').map(category => category.trim()) : [];
    const tagsArray = tags ? tags.split(',').map(tag => tag.trim()) : [];
    
    // Find the blog post
    const blog = await Blog.findById(req.params.id);
    
    if (!blog) {
      return res.status(404).render('pages/404', { 
        title: '404 - Blog Post Not Found' 
      });
    }
    
    // Update blog post fields
    blog.title = title;
    blog.content = content;
    blog.shortDescription = shortDescription;
    blog.categories = categoriesArray;
    blog.tags = tagsArray;
    blog.author = author;
    blog.featured = featured === 'on';
    blog.published = published === 'on';
    
    // Update image if a new one was uploaded
    if (req.file) {
      // Delete old image if it exists
      if (blog.image) {
        const oldImagePath = path.join(__dirname, '../public', blog.image);
        if (fs.existsSync(oldImagePath)) {
          fs.unlinkSync(oldImagePath);
        }
      }
      
      blog.image = `/uploads/blog/${req.file.filename}`;
    }
    
    await blog.save();
    
    res.redirect('/admin/blog');
  } catch (error) {
    console.error('Error updating blog post:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while updating the blog post.' 
    });
  }
};

// Admin: Delete a blog post
exports.deleteBlogPost = async (req, res) => {
  try {
    const blog = await Blog.findById(req.params.id);
    
    if (!blog) {
      return res.status(404).json({ success: false, message: 'Blog post not found' });
    }
    
    // Delete associated image
    if (blog.image) {
      const imagePath = path.join(__dirname, '../public', blog.image);
      if (fs.existsSync(imagePath)) {
        fs.unlinkSync(imagePath);
      }
    }
    
    await Blog.findByIdAndDelete(req.params.id);
    
    res.redirect('/admin/blog');
  } catch (error) {
    console.error('Error deleting blog post:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while deleting the blog post.' 
    });
  }
};
