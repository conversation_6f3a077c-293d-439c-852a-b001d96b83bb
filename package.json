{"name": "scienceclub", "version": "1.0.0", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "seed": "node scripts/seed.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "connect-mongo": "^5.1.0", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^5.1.0", "express-session": "^1.18.1", "mongoose": "^8.15.1", "multer": "^2.0.0", "nodemailer": "^7.0.3", "tailwindcss": "^4.1.8"}, "devDependencies": {"nodemon": "^3.1.10"}}