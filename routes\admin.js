const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const projectController = require('../controllers/projectController');
const blogController = require('../controllers/blogController');
const resourceController = require('../controllers/resourceController');
const eventController = require('../controllers/eventController');
const contactController = require('../controllers/contactController');

// Authentication routes
router.get('/login', adminController.getLoginPage);
router.post('/login', adminController.login);
router.get('/logout', adminController.logout);

// Protected routes (require authentication)
router.use(adminController.requireAuth);

// Dashboard
router.get('/', adminController.getDashboard);
router.get('/dashboard', adminController.getDashboard);

// User management
router.get('/users', adminController.getUserManagement);
router.get('/users/create', adminController.getCreateUserForm);
router.post('/users/create', adminController.createUser);
router.delete('/users/:id', adminController.deleteUser);

// Settings
router.get('/settings', adminController.getSettings);
router.post('/settings', adminController.updateProfile);

// Projects management
router.get('/projects', projectController.getAdminProjects);
router.get('/projects/create', projectController.getCreateProjectForm);
router.post('/projects/create', projectController.uploadFiles, projectController.createProject);
router.get('/projects/:id/edit', projectController.getEditProjectForm);
router.post('/projects/:id/edit', projectController.uploadFiles, projectController.updateProject);
router.delete('/projects/:id', projectController.deleteProject);

// Blog management
router.get('/blog', blogController.getAdminBlogPosts);
router.get('/blog/create', blogController.getCreateBlogForm);
router.post('/blog/create', blogController.uploadImage, blogController.createBlogPost);
router.get('/blog/:id/edit', blogController.getEditBlogForm);
router.post('/blog/:id/edit', blogController.uploadImage, blogController.updateBlogPost);
router.delete('/blog/:id', blogController.deleteBlogPost);

// Resources management
router.get('/resources', resourceController.getAdminResources);
router.get('/resources/create', resourceController.getCreateResourceForm);
router.post('/resources/create', resourceController.uploadFile, resourceController.createResource);
router.get('/resources/:id/edit', resourceController.getEditResourceForm);
router.post('/resources/:id/edit', resourceController.uploadFile, resourceController.updateResource);
router.delete('/resources/:id', resourceController.deleteResource);

// Events management
router.get('/events', eventController.getAdminEvents);
router.get('/events/create', eventController.getCreateEventForm);
router.post('/events/create', eventController.uploadImage, eventController.createEvent);
router.get('/events/:id/edit', eventController.getEditEventForm);
router.post('/events/:id/edit', eventController.uploadImage, eventController.updateEvent);
router.delete('/events/:id', eventController.deleteEvent);

// Contact messages
router.get('/contacts', contactController.getAdminContacts);
router.get('/contacts/:id', contactController.getContactById);
router.post('/contacts/:id/toggle-read', contactController.toggleContactRead);
router.delete('/contacts/:id', contactController.deleteContact);

// API routes for statistics
router.get('/api/contact-stats', contactController.getContactStats);

module.exports = router;
