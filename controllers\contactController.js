const { Contact } = require('../models');
const nodemailer = require('nodemailer');

// Configure nodemailer (you can customize this based on your email provider)
const createTransporter = () => {
  if (process.env.EMAIL_USER && process.env.EMAIL_PASS) {
    return nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });
  }
  return null;
};

// Get contact page
exports.getContactPage = (req, res) => {
  res.render('pages/contact/index', {
    title: 'Science Club - Contact Us'
  });
};

// Handle contact form submission
exports.submitContactForm = async (req, res) => {
  try {
    const { name, email, subject, message, phone } = req.body;
    
    // Validate required fields
    if (!name || !email || !message) {
      return res.status(400).render('pages/contact/index', {
        title: 'Science Club - Contact Us',
        error: 'Please fill in all required fields.',
        formData: req.body
      });
    }
    
    // Create new contact entry
    const newContact = new Contact({
      name,
      email,
      subject: subject || 'Contact Form Submission',
      message,
      phone: phone || null
    });
    
    await newContact.save();
    
    // Send email notification if configured
    const transporter = createTransporter();
    if (transporter) {
      try {
        // Email to admin
        await transporter.sendMail({
          from: process.env.EMAIL_USER,
          to: process.env.EMAIL_USER,
          subject: `New Contact Form Submission: ${subject || 'Contact Form'}`,
          html: `
            <h3>New Contact Form Submission</h3>
            <p><strong>Name:</strong> ${name}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Phone:</strong> ${phone || 'Not provided'}</p>
            <p><strong>Subject:</strong> ${subject || 'Contact Form Submission'}</p>
            <p><strong>Message:</strong></p>
            <p>${message.replace(/\n/g, '<br>')}</p>
            <p><strong>Submitted at:</strong> ${new Date().toLocaleString()}</p>
          `
        });
        
        // Auto-reply to user
        await transporter.sendMail({
          from: process.env.EMAIL_USER,
          to: email,
          subject: 'Thank you for contacting Science Club',
          html: `
            <h3>Thank you for your message!</h3>
            <p>Dear ${name},</p>
            <p>Thank you for reaching out to Science Club. We have received your message and will get back to you as soon as possible.</p>
            <p><strong>Your message:</strong></p>
            <p>${message.replace(/\n/g, '<br>')}</p>
            <p>Best regards,<br>Science Club Team</p>
          `
        });
      } catch (emailError) {
        console.error('Error sending email:', emailError);
        // Continue even if email fails
      }
    }
    
    res.render('pages/contact/success', {
      title: 'Science Club - Message Sent',
      name
    });
  } catch (error) {
    console.error('Error submitting contact form:', error);
    res.status(500).render('pages/contact/index', {
      title: 'Science Club - Contact Us',
      error: 'An error occurred while sending your message. Please try again.',
      formData: req.body
    });
  }
};

// Admin: Get all contact messages
exports.getAdminContacts = async (req, res) => {
  try {
    const contacts = await Contact.find().sort({ createdAt: -1 });
    
    res.render('admin/contacts/index', {
      title: 'Admin - Contact Messages',
      contacts
    });
  } catch (error) {
    console.error('Error fetching admin contacts:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching contact messages.' 
    });
  }
};

// Admin: View a single contact message
exports.getContactById = async (req, res) => {
  try {
    const contact = await Contact.findById(req.params.id);
    
    if (!contact) {
      return res.status(404).render('pages/404', { 
        title: '404 - Contact Message Not Found' 
      });
    }
    
    // Mark as read
    if (!contact.isRead) {
      contact.isRead = true;
      await contact.save();
    }
    
    res.render('admin/contacts/show', {
      title: `Admin - Contact Message from ${contact.name}`,
      contact
    });
  } catch (error) {
    console.error('Error fetching contact message:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching the contact message.' 
    });
  }
};

// Admin: Mark contact as read/unread
exports.toggleContactRead = async (req, res) => {
  try {
    const contact = await Contact.findById(req.params.id);
    
    if (!contact) {
      return res.status(404).json({ error: 'Contact message not found' });
    }
    
    contact.isRead = !contact.isRead;
    await contact.save();
    
    res.json({ success: true, isRead: contact.isRead });
  } catch (error) {
    console.error('Error toggling contact read status:', error);
    res.status(500).json({ error: 'An error occurred while updating the contact message.' });
  }
};

// Admin: Delete a contact message
exports.deleteContact = async (req, res) => {
  try {
    const contact = await Contact.findById(req.params.id);
    
    if (!contact) {
      return res.status(404).render('pages/404', { 
        title: '404 - Contact Message Not Found' 
      });
    }
    
    await Contact.findByIdAndDelete(req.params.id);
    
    res.redirect('/admin/contacts');
  } catch (error) {
    console.error('Error deleting contact message:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while deleting the contact message.' 
    });
  }
};

// Admin: Get contact statistics
exports.getContactStats = async (req, res) => {
  try {
    const totalContacts = await Contact.countDocuments();
    const unreadContacts = await Contact.countDocuments({ isRead: false });
    const todayContacts = await Contact.countDocuments({
      createdAt: {
        $gte: new Date(new Date().setHours(0, 0, 0, 0))
      }
    });
    
    res.json({
      total: totalContacts,
      unread: unreadContacts,
      today: todayContacts
    });
  } catch (error) {
    console.error('Error fetching contact stats:', error);
    res.status(500).json({ error: 'An error occurred while fetching contact statistics.' });
  }
};
