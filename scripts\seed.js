const mongoose = require('mongoose');
const { User, Project, Blog, Resource, Event } = require('../models');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/scienceclub')
  .then(() => console.log('MongoDB connected for seeding'))
  .catch(err => console.error('MongoDB connection error:', err));

async function seedDatabase() {
  try {
    console.log('Starting database seeding...');

    // Create default admin user
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    if (!existingAdmin) {
      const adminUser = new User({
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'admin123', // This will be hashed automatically
        role: 'admin'
      });
      await adminUser.save();
      console.log('✓ Default admin user created (email: <EMAIL>, password: admin123)');
    } else {
      console.log('✓ Admin user already exists');
    }

    // Create sample projects
    const projectCount = await Project.countDocuments();
    if (projectCount === 0) {
      const sampleProjects = [
        {
          title: 'AI-Powered Climate Prediction Model',
          description: 'Developing machine learning algorithms to predict climate patterns and extreme weather events with improved accuracy. This project combines meteorological data with advanced neural networks to provide better forecasting capabilities for environmental planning.',
          authors: ['Sarah Johnson', 'Michael Chen'],
          tags: ['AI', 'Climate Science', 'Machine Learning', 'Environmental'],
          featured: true,
          image: null
        },
        {
          title: 'Biodegradable Plastic Alternatives from Algae',
          description: 'Research into creating sustainable plastic alternatives using algae-based materials. Our team is exploring various algae species and processing methods to develop biodegradable polymers that could replace traditional plastics in packaging applications.',
          authors: ['Emily Rodriguez', 'David Kim'],
          tags: ['Sustainability', 'Materials Science', 'Biotechnology'],
          featured: true,
          image: null
        },
        {
          title: 'Quantum Computing Applications in Drug Discovery',
          description: 'Investigating how quantum computing can accelerate the drug discovery process by simulating molecular interactions at unprecedented scales. This interdisciplinary project bridges quantum physics and pharmaceutical research.',
          authors: ['Alex Thompson', 'Lisa Wang'],
          tags: ['Quantum Computing', 'Drug Discovery', 'Physics', 'Chemistry'],
          featured: false,
          image: null
        }
      ];

      await Project.insertMany(sampleProjects);
      console.log('✓ Sample projects created');
    } else {
      console.log('✓ Projects already exist');
    }

    // Create sample blog posts
    const blogCount = await Blog.countDocuments();
    if (blogCount === 0) {
      const adminUser = await User.findOne({ email: '<EMAIL>' });
      
      const sampleBlogs = [
        {
          title: 'The Future of Renewable Energy Research',
          slug: 'future-renewable-energy-research',
          content: 'Renewable energy research is at a critical juncture. With climate change accelerating and fossil fuel reserves depleting, the scientific community is racing to develop more efficient and cost-effective renewable energy solutions. Recent breakthroughs in solar panel efficiency, wind turbine design, and energy storage systems are paving the way for a sustainable future. Our research team has been investigating novel photovoltaic materials that could increase solar panel efficiency by up to 40%. Additionally, we are exploring advanced battery technologies that could store renewable energy for extended periods, making clean energy available even when the sun isn\'t shining or the wind isn\'t blowing.',
          shortDescription: 'Exploring the latest breakthroughs in renewable energy research and their potential impact on our sustainable future.',
          author: adminUser._id,
          categories: ['Energy', 'Sustainability', 'Research'],
          tags: ['Solar', 'Wind', 'Battery Technology'],
          featured: true,
          published: true,
          image: null
        },
        {
          title: 'Breakthrough in Gene Therapy for Rare Diseases',
          slug: 'breakthrough-gene-therapy-rare-diseases',
          content: 'Gene therapy has emerged as a promising treatment option for rare genetic diseases that previously had no cure. Recent clinical trials have shown remarkable success in treating conditions such as sickle cell disease, beta-thalassemia, and certain forms of inherited blindness. The CRISPR-Cas9 gene editing technology has revolutionized our ability to precisely modify genetic sequences, offering hope to millions of patients worldwide. Our laboratory has been working on developing safer and more efficient delivery methods for gene therapy, focusing on reducing potential side effects while maximizing therapeutic benefits.',
          shortDescription: 'Recent advances in gene therapy are offering new hope for patients with rare genetic diseases.',
          author: adminUser._id,
          categories: ['Medicine', 'Genetics', 'Biotechnology'],
          tags: ['CRISPR', 'Gene Editing', 'Clinical Trials'],
          featured: true,
          published: true,
          image: null
        },
        {
          title: 'Artificial Intelligence in Scientific Discovery',
          slug: 'artificial-intelligence-scientific-discovery',
          content: 'Artificial intelligence is transforming the way we conduct scientific research. From drug discovery to materials science, AI algorithms are helping researchers identify patterns, generate hypotheses, and accelerate the pace of discovery. Machine learning models can now predict protein structures, identify potential drug compounds, and even design new materials with specific properties. Our research group has been developing AI tools that can analyze vast datasets from scientific literature and experimental results to suggest new research directions and potential collaborations.',
          shortDescription: 'How artificial intelligence is revolutionizing scientific research and accelerating discoveries across multiple disciplines.',
          author: adminUser._id,
          categories: ['AI', 'Research Methods', 'Technology'],
          tags: ['Machine Learning', 'Data Science', 'Innovation'],
          featured: false,
          published: true,
          image: null
        }
      ];

      await Blog.insertMany(sampleBlogs);
      console.log('✓ Sample blog posts created');
    } else {
      console.log('✓ Blog posts already exist');
    }

    // Create sample resources
    const resourceCount = await Resource.countDocuments();
    if (resourceCount === 0) {
      const sampleResources = [
        {
          title: 'Introduction to Machine Learning in Science',
          description: 'A comprehensive guide to applying machine learning techniques in scientific research, including practical examples and case studies.',
          type: 'link',
          url: 'https://example.com/ml-science-guide',
          category: 'Tutorials',
          tags: ['Machine Learning', 'Data Science', 'Tutorial'],
          featured: true
        },
        {
          title: 'Climate Data Analysis Toolkit',
          description: 'A collection of Python tools and scripts for analyzing climate data, including visualization and statistical analysis functions.',
          type: 'link',
          url: 'https://github.com/example/climate-toolkit',
          category: 'Tools',
          tags: ['Climate Science', 'Python', 'Data Analysis'],
          featured: true
        },
        {
          title: 'Research Methodology Best Practices',
          description: 'Essential guidelines for conducting rigorous scientific research, covering experimental design, data collection, and statistical analysis.',
          type: 'link',
          url: 'https://example.com/research-methodology',
          category: 'Research Papers',
          tags: ['Methodology', 'Statistics', 'Research Design'],
          featured: false
        }
      ];

      await Resource.insertMany(sampleResources);
      console.log('✓ Sample resources created');
    } else {
      console.log('✓ Resources already exist');
    }

    // Create sample events
    const eventCount = await Event.countDocuments();
    if (eventCount === 0) {
      const now = new Date();
      const futureDate1 = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 1 week from now
      const futureDate2 = new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000); // 2 weeks from now
      const futureDate3 = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 1 month from now

      const sampleEvents = [
        {
          title: 'Annual Science Fair 2024',
          description: 'Join us for our annual science fair where students present their research projects and compete for awards. This year\'s theme is "Innovation for Sustainability" and we expect over 100 project presentations across various scientific disciplines.',
          date: futureDate3,
          location: 'University Main Auditorium',
          organizer: 'Science Club',
          featured: true,
          image: null
        },
        {
          title: 'Workshop: Introduction to Data Science',
          description: 'A hands-on workshop covering the fundamentals of data science, including data cleaning, visualization, and basic machine learning techniques. Perfect for beginners looking to get started in data science.',
          date: futureDate1,
          location: 'Computer Lab B-205',
          organizer: 'Science Club',
          featured: true,
          image: null
        },
        {
          title: 'Guest Lecture: Climate Change and Technology',
          description: 'Dr. Maria Santos from the National Climate Research Institute will discuss the latest technological solutions for addressing climate change, including renewable energy innovations and carbon capture technologies.',
          date: futureDate2,
          location: 'Lecture Hall A-101',
          organizer: 'Science Club',
          featured: false,
          image: null
        }
      ];

      await Event.insertMany(sampleEvents);
      console.log('✓ Sample events created');
    } else {
      console.log('✓ Events already exist');
    }

    console.log('\n🎉 Database seeding completed successfully!');
    console.log('\nDefault admin credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
    console.log('\nYou can now start the application and log in to the admin panel.');

  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the seeding function
seedDatabase();
