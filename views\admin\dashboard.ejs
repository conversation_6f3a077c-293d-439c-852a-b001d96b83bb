<!-- Admin Dashboard -->

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    <!-- Projects Stats -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-flask text-blue-600 text-xl"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900"><%= stats.projects %></h3>
                <p class="text-gray-600">Total Projects</p>
            </div>
        </div>
        <div class="mt-4">
            <a href="/admin/projects" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                View all projects <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
    </div>
    
    <!-- Blog Stats -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-newspaper text-green-600 text-xl"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900"><%= stats.blogs %></h3>
                <p class="text-gray-600">Blog Posts</p>
            </div>
        </div>
        <div class="mt-4">
            <a href="/admin/blog" class="text-green-600 hover:text-green-800 text-sm font-medium">
                Manage blog <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
    </div>
    
    <!-- Resources Stats -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-book text-purple-600 text-xl"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900"><%= stats.resources %></h3>
                <p class="text-gray-600">Resources</p>
            </div>
        </div>
        <div class="mt-4">
            <a href="/admin/resources" class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                View resources <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
    </div>
    
    <!-- Events Stats -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-calendar text-yellow-600 text-xl"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900"><%= stats.events %></h3>
                <p class="text-gray-600">Total Events</p>
            </div>
        </div>
        <div class="mt-4">
            <a href="/admin/events" class="text-yellow-600 hover:text-yellow-800 text-sm font-medium">
                Manage events <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
    </div>
    
    <!-- Messages Stats -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-envelope text-red-600 text-xl"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900"><%= stats.contacts %></h3>
                <p class="text-gray-600">Total Messages</p>
                <% if (stats.unreadContacts > 0) { %>
                    <p class="text-red-600 text-sm font-medium"><%= stats.unreadContacts %> unread</p>
                <% } %>
            </div>
        </div>
        <div class="mt-4">
            <a href="/admin/contacts" class="text-red-600 hover:text-red-800 text-sm font-medium">
                View messages <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Quick Actions Card -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
            <i class="fas fa-bolt mr-2 text-blue-600"></i>Quick Actions
        </h2>
        <div class="grid grid-cols-2 gap-4">
            <a href="/admin/projects/create" class="btn-primary text-center py-3">
                <i class="fas fa-plus mr-2"></i>New Project
            </a>
            <a href="/admin/blog/create" class="btn-success text-center py-3">
                <i class="fas fa-plus mr-2"></i>New Post
            </a>
            <a href="/admin/events/create" class="btn-secondary text-center py-3">
                <i class="fas fa-plus mr-2"></i>New Event
            </a>
            <a href="/admin/resources/create" class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 text-center">
                <i class="fas fa-plus mr-2"></i>New Resource
            </a>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">
            <i class="fas fa-clock mr-2 text-green-600"></i>Recent Activity
        </h2>
        <div class="space-y-3">
            <div class="flex items-center text-sm">
                <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                <span class="text-gray-600">New project added: "AI Research"</span>
                <span class="text-gray-400 ml-auto">2h ago</span>
            </div>
            <div class="flex items-center text-sm">
                <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                <span class="text-gray-600">Blog post published: "Latest Discoveries"</span>
                <span class="text-gray-400 ml-auto">4h ago</span>
            </div>
            <div class="flex items-center text-sm">
                <div class="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                <span class="text-gray-600">Event scheduled: "Science Fair 2024"</span>
                <span class="text-gray-400 ml-auto">1d ago</span>
            </div>
            <div class="flex items-center text-sm">
                <div class="w-2 h-2 bg-red-500 rounded-full mr-3"></div>
                <span class="text-gray-600">New contact message received</span>
                <span class="text-gray-400 ml-auto">2d ago</span>
            </div>
        </div>
    </div>
</div>

<!-- Recent Content -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Recent Projects -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-900">
                <i class="fas fa-flask mr-2 text-blue-600"></i>Recent Projects
            </h2>
            <a href="/admin/projects" class="text-blue-600 hover:text-blue-800 text-sm">View all</a>
        </div>
        <% if (recentProjects && recentProjects.length > 0) { %>
            <div class="space-y-3">
                <% recentProjects.forEach(project => { %>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <h3 class="font-medium text-gray-900"><%= project.title %></h3>
                            <p class="text-sm text-gray-600">
                                <%= new Date(project.createdAt).toLocaleDateString() %>
                            </p>
                        </div>
                        <div class="flex space-x-2">
                            <a href="/admin/projects/<%= project._id %>/edit" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="/projects/<%= project._id %>" target="_blank" class="text-green-600 hover:text-green-800">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    </div>
                <% }) %>
            </div>
        <% } else { %>
            <p class="text-gray-500 text-center py-4">No recent projects</p>
        <% } %>
    </div>
    
    <!-- Recent Blog Posts -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-900">
                <i class="fas fa-newspaper mr-2 text-green-600"></i>Recent Blog Posts
            </h2>
            <a href="/admin/blog" class="text-green-600 hover:text-green-800 text-sm">View all</a>
        </div>
        <% if (recentBlogs && recentBlogs.length > 0) { %>
            <div class="space-y-3">
                <% recentBlogs.forEach(blog => { %>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <h3 class="font-medium text-gray-900"><%= blog.title %></h3>
                            <p class="text-sm text-gray-600">
                                By <%= blog.author ? blog.author.name : 'Unknown' %> • 
                                <%= new Date(blog.createdAt).toLocaleDateString() %>
                            </p>
                        </div>
                        <div class="flex space-x-2">
                            <a href="/admin/blog/<%= blog._id %>/edit" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="/blog/<%= blog.slug %>" target="_blank" class="text-green-600 hover:text-green-800">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    </div>
                <% }) %>
            </div>
        <% } else { %>
            <p class="text-gray-500 text-center py-4">No recent blog posts</p>
        <% } %>
    </div>
</div>

<!-- Recent Messages and Upcoming Events -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
    <!-- Recent Messages -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-900">
                <i class="fas fa-envelope mr-2 text-red-600"></i>Recent Messages
            </h2>
            <a href="/admin/contacts" class="text-red-600 hover:text-red-800 text-sm">View all</a>
        </div>
        <% if (recentContacts && recentContacts.length > 0) { %>
            <div class="space-y-3">
                <% recentContacts.forEach(contact => { %>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <h3 class="font-medium text-gray-900"><%= contact.name %></h3>
                            <p class="text-sm text-gray-600"><%= contact.subject %></p>
                            <p class="text-xs text-gray-500">
                                <%= new Date(contact.createdAt).toLocaleDateString() %>
                            </p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <% if (!contact.isRead) { %>
                                <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                            <% } %>
                            <a href="/admin/contacts/<%= contact._id %>" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                <% }) %>
            </div>
        <% } else { %>
            <p class="text-gray-500 text-center py-4">No recent messages</p>
        <% } %>
    </div>
    
    <!-- Upcoming Events -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-900">
                <i class="fas fa-calendar mr-2 text-yellow-600"></i>Upcoming Events
            </h2>
            <a href="/admin/events" class="text-yellow-600 hover:text-yellow-800 text-sm">View all</a>
        </div>
        <% if (upcomingEvents && upcomingEvents.length > 0) { %>
            <div class="space-y-3">
                <% upcomingEvents.forEach(event => { %>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <h3 class="font-medium text-gray-900"><%= event.title %></h3>
                            <p class="text-sm text-gray-600">
                                <%= new Date(event.date).toLocaleDateString() %> • <%= event.location %>
                            </p>
                        </div>
                        <div class="flex space-x-2">
                            <a href="/admin/events/<%= event._id %>/edit" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="/events/<%= event._id %>" target="_blank" class="text-green-600 hover:text-green-800">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    </div>
                <% }) %>
            </div>
        <% } else { %>
            <p class="text-gray-500 text-center py-4">No upcoming events</p>
        <% } %>
    </div>
</div>
