const { Resource } = require('../models');
const path = require('path');
const fs = require('fs');
const multer = require('multer');

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'public/uploads/resources');
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 20 * 1024 * 1024 }, // 20MB limit
  fileFilter: (req, file, cb) => {
    // Accept only PDFs
    const filetypes = /pdf/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    
    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('Only PDF files are allowed!'));
  }
});

// Middleware for handling file uploads
exports.uploadFile = upload.single('file');

// Get all resources
exports.getAllResources = async (req, res) => {
  try {
    // Get all unique categories
    const categories = await Resource.distinct('category');
    
    // Get resources for each category
    const resourcesByCategory = {};
    
    for (const category of categories) {
      resourcesByCategory[category] = await Resource.find({ category })
        .sort({ createdAt: -1 });
    }
    
    res.render('pages/resources/index', {
      title: 'Science Club - Resources',
      categories,
      resourcesByCategory
    });
  } catch (error) {
    console.error('Error fetching resources:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching resources.' 
    });
  }
};

// Get resources by category
exports.getResourcesByCategory = async (req, res) => {
  try {
    const { category } = req.params;
    
    const resources = await Resource.find({ category })
      .sort({ createdAt: -1 });
    
    if (resources.length === 0) {
      return res.status(404).render('pages/404', { 
        title: '404 - Category Not Found' 
      });
    }
    
    res.render('pages/resources/category', {
      title: `Science Club - ${category} Resources`,
      category,
      resources
    });
  } catch (error) {
    console.error('Error fetching resources by category:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching resources.' 
    });
  }
};

// Admin: Get all resources
exports.getAdminResources = async (req, res) => {
  try {
    const resources = await Resource.find()
      .sort({ createdAt: -1 });
    
    res.render('admin/resources/index', {
      title: 'Admin - Resources',
      resources
    });
  } catch (error) {
    console.error('Error fetching admin resources:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching resources.' 
    });
  }
};

// Admin: Get form to create a new resource
exports.getCreateResourceForm = (req, res) => {
  res.render('admin/resources/create', {
    title: 'Admin - Create New Resource'
  });
};

// Admin: Create a new resource
exports.createResource = async (req, res) => {
  try {
    const { title, description, type, url, category, tags, featured } = req.body;
    
    // Process tags (convert comma-separated string to array)
    const tagsArray = tags ? tags.split(',').map(tag => tag.trim()) : [];
    
    // Create new resource
    const newResource = new Resource({
      title,
      description,
      type,
      url: type === 'pdf' && req.file ? `/uploads/resources/${req.file.filename}` : url,
      filePath: type === 'pdf' && req.file ? `/uploads/resources/${req.file.filename}` : null,
      category,
      tags: tagsArray,
      featured: featured === 'on'
    });
    
    await newResource.save();
    
    res.redirect('/admin/resources');
  } catch (error) {
    console.error('Error creating resource:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while creating the resource.' 
    });
  }
};

// Admin: Get form to edit a resource
exports.getEditResourceForm = async (req, res) => {
  try {
    const resource = await Resource.findById(req.params.id);
    
    if (!resource) {
      return res.status(404).render('pages/404', { 
        title: '404 - Resource Not Found' 
      });
    }
    
    res.render('admin/resources/edit', {
      title: `Admin - Edit Resource: ${resource.title}`,
      resource
    });
  } catch (error) {
    console.error('Error fetching resource for edit:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching the resource for editing.' 
    });
  }
};

// Admin: Update a resource
exports.updateResource = async (req, res) => {
  try {
    const { title, description, type, url, category, tags, featured } = req.body;
    
    // Process tags (convert comma-separated string to array)
    const tagsArray = tags ? tags.split(',').map(tag => tag.trim()) : [];
    
    // Find the resource
    const resource = await Resource.findById(req.params.id);
    
    if (!resource) {
      return res.status(404).render('pages/404', { 
        title: '404 - Resource Not Found' 
      });
    }
    
    // Update resource fields
    resource.title = title;
    resource.description = description;
    resource.type = type;
    resource.category = category;
    resource.tags = tagsArray;
    resource.featured = featured === 'on';
    
    // Update URL and file path based on type and file upload
    if (type === 'pdf') {
      if (req.file) {
        // Delete old file if it exists
        if (resource.filePath) {
          const oldFilePath = path.join(__dirname, '../public', resource.filePath);
          if (fs.existsSync(oldFilePath)) {
            fs.unlinkSync(oldFilePath);
          }
        }
        
        resource.url = `/uploads/resources/${req.file.filename}`;
        resource.filePath = `/uploads/resources/${req.file.filename}`;
      }
    } else {
      resource.url = url;
      
      // Delete old file if type changed from pdf to link/video
      if (resource.filePath) {
        const oldFilePath = path.join(__dirname, '../public', resource.filePath);
        if (fs.existsSync(oldFilePath)) {
          fs.unlinkSync(oldFilePath);
        }
        resource.filePath = null;
      }
    }
    
    await resource.save();
    
    res.redirect('/admin/resources');
  } catch (error) {
    console.error('Error updating resource:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while updating the resource.' 
    });
  }
};

// Admin: Delete a resource
exports.deleteResource = async (req, res) => {
  try {
    const resource = await Resource.findById(req.params.id);
    
    if (!resource) {
      return res.status(404).json({ success: false, message: 'Resource not found' });
    }
    
    // Delete associated file if it's a PDF
    if (resource.type === 'pdf' && resource.filePath) {
      const filePath = path.join(__dirname, '../public', resource.filePath);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }
    
    await Resource.findByIdAndDelete(req.params.id);
    
    res.redirect('/admin/resources');
  } catch (error) {
    console.error('Error deleting resource:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while deleting the resource.' 
    });
  }
};
