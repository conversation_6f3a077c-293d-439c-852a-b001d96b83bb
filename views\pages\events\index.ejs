<!-- Events Index Page -->

<!-- Hero Section -->
<section class="bg-gradient-to-r from-purple-600 to-blue-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">Science Events</h1>
            <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
                Join us for workshops, seminars, conferences, and networking events
            </p>
        </div>
    </div>
</section>

<!-- Upcoming Events Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Upcoming Events</h2>
        
        <% if (upcomingEvents && upcomingEvents.length > 0) { %>
            <div class="grid-2 lg:grid-cols-1 gap-8">
                <% upcomingEvents.forEach(event => { %>
                    <div class="bg-white border border-gray-200 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                        <div class="md:flex">
                            <!-- Event Date -->
                            <div class="md:w-32 bg-blue-600 text-white flex flex-col items-center justify-center p-6">
                                <div class="text-sm font-medium uppercase">
                                    <%= new Date(event.date).toLocaleDateString('en-US', { month: 'short' }) %>
                                </div>
                                <div class="text-3xl font-bold">
                                    <%= new Date(event.date).getDate() %>
                                </div>
                                <div class="text-sm">
                                    <%= new Date(event.date).getFullYear() %>
                                </div>
                            </div>
                            
                            <!-- Event Image -->
                            <% if (event.image) { %>
                                <div class="md:w-48">
                                    <img src="<%= event.image %>" alt="<%= event.title %>" class="w-full h-48 md:h-full object-cover">
                                </div>
                            <% } %>
                            
                            <!-- Event Content -->
                            <div class="flex-1 p-6">
                                <% if (event.featured) { %>
                                    <div class="mb-2">
                                        <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                                            <i class="fas fa-star mr-1"></i>Featured
                                        </span>
                                    </div>
                                <% } %>
                                
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">
                                    <a href="/events/<%= event._id %>" class="hover:text-blue-600 transition-colors">
                                        <%= event.title %>
                                    </a>
                                </h3>
                                
                                <p class="text-gray-600 mb-4">
                                    <%= event.description.length > 200 ? event.description.substring(0, 200) + '...' : event.description %>
                                </p>
                                
                                <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500 mb-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-clock mr-1"></i>
                                        <%= new Date(event.date).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) %>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        <%= event.location %>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-user mr-1"></i>
                                        <%= event.organizer %>
                                    </div>
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <a href="/events/<%= event._id %>" class="btn-primary">
                                        <i class="fas fa-info-circle mr-1"></i>Learn More
                                    </a>
                                    
                                    <% if (event.registrationLink) { %>
                                        <a href="<%= event.registrationLink %>" target="_blank" class="btn-success">
                                            <i class="fas fa-external-link-alt mr-1"></i>Register
                                        </a>
                                    <% } %>
                                </div>
                            </div>
                        </div>
                    </div>
                <% }) %>
            </div>
        <% } else { %>
            <div class="text-center py-16">
                <div class="w-32 h-32 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-calendar text-gray-400 text-4xl"></i>
                </div>
                <h3 class="text-2xl font-semibold text-gray-900 mb-4">No Upcoming Events</h3>
                <p class="text-gray-600 mb-8">
                    We don't have any upcoming events scheduled at the moment. 
                    Check back soon for exciting workshops and seminars!
                </p>
                <a href="/contact" class="btn-primary">
                    <i class="fas fa-envelope mr-2"></i>Get Notified
                </a>
            </div>
        <% } %>
    </div>
</section>

<!-- Past Events Section -->
<% if (pastEvents && pastEvents.length > 0) { %>
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Past Events</h2>
            
            <div class="grid-3 gap-6">
                <% pastEvents.slice(0, 6).forEach(event => { %>
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <% if (event.image) { %>
                            <img src="<%= event.image %>" alt="<%= event.title %>" class="w-full h-48 object-cover">
                        <% } else { %>
                            <div class="w-full h-48 bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center">
                                <i class="fas fa-calendar text-white text-3xl"></i>
                            </div>
                        <% } %>
                        
                        <div class="p-6">
                            <div class="text-sm text-gray-500 mb-2">
                                <%= new Date(event.date).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }) %>
                            </div>
                            
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                <a href="/events/<%= event._id %>" class="hover:text-blue-600 transition-colors">
                                    <%= event.title %>
                                </a>
                            </h3>
                            
                            <p class="text-gray-600 text-sm mb-4">
                                <%= event.description.length > 100 ? event.description.substring(0, 100) + '...' : event.description %>
                            </p>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-500">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    <%= event.location %>
                                </span>
                                <a href="/events/<%= event._id %>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    View Details <i class="fas fa-arrow-right ml-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                <% }) %>
            </div>
            
            <% if (pastEvents.length > 6) { %>
                <div class="text-center mt-8">
                    <button class="btn-outline" onclick="loadMorePastEvents()">
                        <i class="fas fa-plus mr-2"></i>Load More Past Events
                    </button>
                </div>
            <% } %>
        </div>
    </section>
<% } %>

<!-- Call to Action -->
<section class="py-16 bg-blue-600 text-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-4">Want to Host an Event?</h2>
        <p class="text-xl mb-8 opacity-90">
            Have an idea for a workshop, seminar, or conference? We'd love to help you organize it!
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/contact" class="bg-white text-blue-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors duration-200">
                <i class="fas fa-lightbulb mr-2"></i>Propose an Event
            </a>
            <a href="/about" class="border-2 border-white text-white hover:bg-white hover:text-blue-600 font-medium py-3 px-8 rounded-lg transition-all duration-200">
                <i class="fas fa-users mr-2"></i>Join Our Team
            </a>
        </div>
    </div>
</section>

<script>
function loadMorePastEvents() {
    // This would typically load more events via AJAX
    // For now, we'll just show a message
    alert('Loading more past events... (This would be implemented with AJAX)');
}
</script>
