@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Science Club */
:root {
  --primary-color: #2563eb;
  --secondary-color: #1e40af;
  --accent-color: #3b82f6;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --dark-color: #1f2937;
  --light-color: #f9fafb;
}

/* Base styles */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

/* Custom components */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-success {
  @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-outline {
  @apply border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;
}

/* Card styles */
.card {
  @apply bg-white rounded-lg shadow-md overflow-hidden;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200;
}

.card-body {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* Form styles */
.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical;
}

.form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.form-checkbox {
  @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

/* Alert styles */
.alert {
  @apply px-4 py-3 rounded-md mb-4;
}

.alert-success {
  @apply bg-green-100 border border-green-400 text-green-700;
}

.alert-error {
  @apply bg-red-100 border border-red-400 text-red-700;
}

.alert-warning {
  @apply bg-yellow-100 border border-yellow-400 text-yellow-700;
}

.alert-info {
  @apply bg-blue-100 border border-blue-400 text-blue-700;
}

/* Navigation styles */
.navbar {
  @apply bg-white shadow-lg;
}

.navbar-brand {
  @apply text-xl font-bold text-blue-600;
}

.navbar-nav {
  @apply flex space-x-6;
}

.nav-link {
  @apply text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200;
}

.nav-link.active {
  @apply text-blue-600;
}

/* Hero section */
.hero {
  @apply bg-gradient-to-r from-blue-600 to-blue-800 text-white;
}

.hero-title {
  @apply text-4xl md:text-6xl font-bold mb-4;
}

.hero-subtitle {
  @apply text-xl md:text-2xl mb-8 opacity-90;
}

/* Section styles */
.section {
  @apply py-16;
}

.section-title {
  @apply text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900;
}

.section-subtitle {
  @apply text-lg text-gray-600 text-center mb-12 max-w-3xl mx-auto;
}

/* Grid layouts */
.grid-1 {
  @apply grid grid-cols-1 gap-6;
}

.grid-2 {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.grid-3 {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.grid-4 {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

/* Project card styles */
.project-card {
  @apply bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300;
}

.project-image {
  @apply w-full h-48 object-cover;
}

.project-content {
  @apply p-6;
}

.project-title {
  @apply text-xl font-semibold mb-2 text-gray-900;
}

.project-description {
  @apply text-gray-600 mb-4;
}

.project-authors {
  @apply text-sm text-blue-600 mb-2;
}

.project-tags {
  @apply flex flex-wrap gap-2;
}

.project-tag {
  @apply bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full;
}

/* Blog card styles */
.blog-card {
  @apply bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300;
}

.blog-image {
  @apply w-full h-48 object-cover;
}

.blog-content {
  @apply p-6;
}

.blog-title {
  @apply text-xl font-semibold mb-2 text-gray-900;
}

.blog-excerpt {
  @apply text-gray-600 mb-4;
}

.blog-meta {
  @apply text-sm text-gray-500 mb-2;
}

.blog-categories {
  @apply flex flex-wrap gap-2;
}

.blog-category {
  @apply bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full;
}

/* Event card styles */
.event-card {
  @apply bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300;
}

.event-date {
  @apply bg-blue-600 text-white text-center py-2;
}

.event-content {
  @apply p-6;
}

.event-title {
  @apply text-xl font-semibold mb-2 text-gray-900;
}

.event-description {
  @apply text-gray-600 mb-4;
}

.event-location {
  @apply text-sm text-gray-500 mb-2;
}

/* Resource card styles */
.resource-card {
  @apply bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300;
}

.resource-icon {
  @apply w-12 h-12 mb-4;
}

.resource-title {
  @apply text-lg font-semibold mb-2 text-gray-900;
}

.resource-description {
  @apply text-gray-600 mb-4;
}

.resource-type {
  @apply inline-block bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full;
}

/* Footer styles */
.footer {
  @apply bg-gray-900 text-white;
}

.footer-content {
  @apply py-12;
}

.footer-section {
  @apply mb-8;
}

.footer-title {
  @apply text-lg font-semibold mb-4;
}

.footer-link {
  @apply text-gray-300 hover:text-white transition-colors duration-200;
}

.footer-bottom {
  @apply border-t border-gray-800 py-6 text-center text-gray-400;
}

/* Admin styles */
.admin-sidebar {
  @apply bg-gray-900 text-white w-64 min-h-screen;
}

.admin-nav {
  @apply py-4;
}

.admin-nav-item {
  @apply block px-6 py-3 text-gray-300 hover:bg-gray-800 hover:text-white transition-colors duration-200;
}

.admin-nav-item.active {
  @apply bg-blue-600 text-white;
}

.admin-content {
  @apply flex-1 p-6;
}

.admin-header {
  @apply mb-6;
}

.admin-title {
  @apply text-2xl font-bold text-gray-900;
}

/* Table styles */
.table {
  @apply w-full border-collapse;
}

.table th {
  @apply bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200;
}

.table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200;
}

.table tbody tr:hover {
  @apply bg-gray-50;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .hero-title {
    @apply text-3xl;
  }
  
  .hero-subtitle {
    @apply text-lg;
  }
  
  .section-title {
    @apply text-2xl;
  }
}

/* Loading spinner */
.spinner {
  @apply inline-block w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
