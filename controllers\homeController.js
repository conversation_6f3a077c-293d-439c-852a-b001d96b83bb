const { Project, Blog, Event } = require('../models');

// Home page controller
exports.getHomePage = async (req, res) => {
  try {
    // Get featured projects
    const featuredProjects = await Project.find({ featured: true })
      .sort({ createdAt: -1 })
      .limit(3);
    
    // Get latest blog posts
    const latestBlogs = await Blog.find({ published: true })
      .sort({ createdAt: -1 })
      .limit(3)
      .populate('author', 'name');
    
    // Get upcoming events
    const upcomingEvents = await Event.find({ 
      date: { $gte: new Date() },
      isPast: false
    })
      .sort({ date: 1 })
      .limit(3);
    
    res.render('pages/home', {
      title: 'Science Club - Home',
      featuredProjects,
      latestBlogs,
      upcomingEvents
    });
  } catch (error) {
    console.error('Error in home controller:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while loading the home page.' 
    });
  }
};
