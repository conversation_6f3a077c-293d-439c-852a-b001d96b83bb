<!-- Projects Index Page -->

<!-- Hero Section -->
<section class="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">Research Projects</h1>
            <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
                Discover innovative research projects led by our talented student researchers
            </p>
        </div>
    </div>
</section>

<!-- Filter and Search Section -->
<section class="py-8 bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
            <!-- Search Bar -->
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <input 
                        type="text" 
                        id="project-search"
                        placeholder="Search projects..." 
                        class="form-input pl-10 w-full"
                    >
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <!-- Filter Options -->
            <div class="flex gap-4">
                <select id="tag-filter" class="form-select">
                    <option value="">All Tags</option>
                    <option value="AI">AI</option>
                    <option value="Climate Science">Climate Science</option>
                    <option value="Biotechnology">Biotechnology</option>
                    <option value="Physics">Physics</option>
                    <option value="Chemistry">Chemistry</option>
                </select>
                
                <select id="sort-filter" class="form-select">
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="title">Title A-Z</option>
                    <option value="featured">Featured First</option>
                </select>
            </div>
        </div>
    </div>
</section>

<!-- Projects Grid -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <% if (projects && projects.length > 0) { %>
            <div class="grid-3" id="projects-grid">
                <% projects.forEach(project => { %>
                    <div class="project-card" data-tags="<%= project.tags ? project.tags.join(',') : '' %>">
                        <% if (project.image) { %>
                            <img src="<%= project.image %>" alt="<%= project.title %>" class="project-image">
                        <% } else { %>
                            <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                                <i class="fas fa-flask text-white text-4xl"></i>
                            </div>
                        <% } %>
                        
                        <div class="project-content">
                            <% if (project.featured) { %>
                                <div class="mb-2">
                                    <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                                        <i class="fas fa-star mr-1"></i>Featured
                                    </span>
                                </div>
                            <% } %>
                            
                            <h3 class="project-title"><%= project.title %></h3>
                            <p class="project-description">
                                <%= project.description.length > 150 ? project.description.substring(0, 150) + '...' : project.description %>
                            </p>
                            
                            <% if (project.authors && project.authors.length > 0) { %>
                                <div class="project-authors">
                                    <i class="fas fa-user mr-1"></i>
                                    <%= project.authors.join(', ') %>
                                </div>
                            <% } %>
                            
                            <% if (project.tags && project.tags.length > 0) { %>
                                <div class="project-tags">
                                    <% project.tags.slice(0, 3).forEach(tag => { %>
                                        <span class="project-tag"><%= tag %></span>
                                    <% }) %>
                                    <% if (project.tags.length > 3) { %>
                                        <span class="project-tag">+<%= project.tags.length - 3 %> more</span>
                                    <% } %>
                                </div>
                            <% } %>
                            
                            <div class="mt-6 flex items-center justify-between">
                                <a href="/projects/<%= project._id %>" class="btn-primary">
                                    <i class="fas fa-arrow-right mr-1"></i>Learn More
                                </a>
                                
                                <% if (project.pdfFile) { %>
                                    <a href="<%= project.pdfFile %>" target="_blank" class="text-red-600 hover:text-red-800" title="Download PDF">
                                        <i class="fas fa-file-pdf text-xl"></i>
                                    </a>
                                <% } %>
                            </div>
                        </div>
                    </div>
                <% }) %>
            </div>
        <% } else { %>
            <div class="text-center py-16">
                <div class="w-32 h-32 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-flask text-gray-400 text-4xl"></i>
                </div>
                <h3 class="text-2xl font-semibold text-gray-900 mb-4">No Projects Found</h3>
                <p class="text-gray-600 mb-8">
                    We don't have any research projects to display at the moment. 
                    Check back soon for exciting new research!
                </p>
                <a href="/contact" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>Submit Your Project
                </a>
            </div>
        <% } %>
    </div>
</section>

<!-- Call to Action -->
<section class="py-16 bg-blue-600 text-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-4">Have a Research Project?</h2>
        <p class="text-xl mb-8 opacity-90">
            Share your research with our community and get feedback from fellow researchers.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/contact" class="bg-white text-blue-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors duration-200">
                <i class="fas fa-upload mr-2"></i>Submit Project
            </a>
            <a href="/about" class="border-2 border-white text-white hover:bg-white hover:text-blue-600 font-medium py-3 px-8 rounded-lg transition-all duration-200">
                <i class="fas fa-info-circle mr-2"></i>Learn More
            </a>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('project-search');
    const tagFilter = document.getElementById('tag-filter');
    const sortFilter = document.getElementById('sort-filter');
    const projectsGrid = document.getElementById('projects-grid');
    const projectCards = Array.from(document.querySelectorAll('.project-card'));

    function filterAndSortProjects() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedTag = tagFilter.value;
        const sortBy = sortFilter.value;

        // Filter projects
        let filteredProjects = projectCards.filter(card => {
            const title = card.querySelector('.project-title').textContent.toLowerCase();
            const description = card.querySelector('.project-description').textContent.toLowerCase();
            const tags = card.getAttribute('data-tags').toLowerCase();
            
            const matchesSearch = title.includes(searchTerm) || description.includes(searchTerm) || tags.includes(searchTerm);
            const matchesTag = !selectedTag || tags.includes(selectedTag.toLowerCase());
            
            return matchesSearch && matchesTag;
        });

        // Sort projects
        filteredProjects.sort((a, b) => {
            switch (sortBy) {
                case 'title':
                    return a.querySelector('.project-title').textContent.localeCompare(b.querySelector('.project-title').textContent);
                case 'featured':
                    const aFeatured = a.querySelector('.bg-yellow-100') ? 1 : 0;
                    const bFeatured = b.querySelector('.bg-yellow-100') ? 1 : 0;
                    return bFeatured - aFeatured;
                case 'oldest':
                    return Array.prototype.indexOf.call(projectCards, a) - Array.prototype.indexOf.call(projectCards, b);
                default: // newest
                    return Array.prototype.indexOf.call(projectCards, b) - Array.prototype.indexOf.call(projectCards, a);
            }
        });

        // Hide all cards
        projectCards.forEach(card => card.style.display = 'none');

        // Show filtered and sorted cards
        filteredProjects.forEach(card => card.style.display = 'block');

        // Show no results message if needed
        if (filteredProjects.length === 0) {
            if (!document.getElementById('no-results')) {
                const noResults = document.createElement('div');
                noResults.id = 'no-results';
                noResults.className = 'col-span-full text-center py-16';
                noResults.innerHTML = `
                    <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-search text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No Projects Found</h3>
                    <p class="text-gray-600">Try adjusting your search criteria or filters.</p>
                `;
                projectsGrid.appendChild(noResults);
            }
        } else {
            const noResults = document.getElementById('no-results');
            if (noResults) {
                noResults.remove();
            }
        }
    }

    // Event listeners
    searchInput.addEventListener('input', filterAndSortProjects);
    tagFilter.addEventListener('change', filterAndSortProjects);
    sortFilter.addEventListener('change', filterAndSortProjects);
});
</script>
