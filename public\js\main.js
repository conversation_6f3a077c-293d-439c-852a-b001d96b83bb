// Science Club Website JavaScript

document.addEventListener('DOMContentLoaded', function() {
  // Initialize all components
  initMobileMenu();
  initFormValidation();
  initImagePreview();
  initConfirmDialogs();
  initTooltips();
  initSmoothScrolling();
  initSearchFunctionality();
  initAdminFeatures();
});

// Mobile menu toggle
function initMobileMenu() {
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', function() {
      mobileMenu.classList.toggle('hidden');
    });
  }
}

// Form validation
function initFormValidation() {
  const forms = document.querySelectorAll('form[data-validate]');
  
  forms.forEach(form => {
    form.addEventListener('submit', function(e) {
      if (!validateForm(form)) {
        e.preventDefault();
      }
    });
  });
}

function validateForm(form) {
  let isValid = true;
  const requiredFields = form.querySelectorAll('[required]');
  
  requiredFields.forEach(field => {
    if (!field.value.trim()) {
      showFieldError(field, 'This field is required');
      isValid = false;
    } else {
      clearFieldError(field);
    }
  });
  
  // Email validation
  const emailFields = form.querySelectorAll('input[type="email"]');
  emailFields.forEach(field => {
    if (field.value && !isValidEmail(field.value)) {
      showFieldError(field, 'Please enter a valid email address');
      isValid = false;
    }
  });
  
  return isValid;
}

function showFieldError(field, message) {
  clearFieldError(field);
  
  field.classList.add('border-red-500');
  const errorDiv = document.createElement('div');
  errorDiv.className = 'text-red-500 text-sm mt-1';
  errorDiv.textContent = message;
  errorDiv.setAttribute('data-error', 'true');
  
  field.parentNode.appendChild(errorDiv);
}

function clearFieldError(field) {
  field.classList.remove('border-red-500');
  const errorDiv = field.parentNode.querySelector('[data-error]');
  if (errorDiv) {
    errorDiv.remove();
  }
}

function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Image preview for file uploads
function initImagePreview() {
  const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
  
  imageInputs.forEach(input => {
    input.addEventListener('change', function(e) {
      const file = e.target.files[0];
      const previewId = input.getAttribute('data-preview');
      const preview = document.getElementById(previewId);
      
      if (file && preview) {
        const reader = new FileReader();
        reader.onload = function(e) {
          preview.src = e.target.result;
          preview.classList.remove('hidden');
        };
        reader.readAsDataURL(file);
      }
    });
  });
}

// Confirm dialogs for delete actions
function initConfirmDialogs() {
  const deleteButtons = document.querySelectorAll('[data-confirm]');
  
  deleteButtons.forEach(button => {
    button.addEventListener('click', function(e) {
      const message = button.getAttribute('data-confirm');
      if (!confirm(message)) {
        e.preventDefault();
      }
    });
  });
}

// Tooltips
function initTooltips() {
  const tooltipElements = document.querySelectorAll('[data-tooltip]');
  
  tooltipElements.forEach(element => {
    element.addEventListener('mouseenter', showTooltip);
    element.addEventListener('mouseleave', hideTooltip);
  });
}

function showTooltip(e) {
  const element = e.target;
  const tooltipText = element.getAttribute('data-tooltip');
  
  const tooltip = document.createElement('div');
  tooltip.className = 'absolute bg-gray-900 text-white text-sm px-2 py-1 rounded shadow-lg z-50';
  tooltip.textContent = tooltipText;
  tooltip.id = 'tooltip';
  
  document.body.appendChild(tooltip);
  
  const rect = element.getBoundingClientRect();
  tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
  tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
}

function hideTooltip() {
  const tooltip = document.getElementById('tooltip');
  if (tooltip) {
    tooltip.remove();
  }
}

// Smooth scrolling for anchor links
function initSmoothScrolling() {
  const anchorLinks = document.querySelectorAll('a[href^="#"]');
  
  anchorLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      
      const targetId = this.getAttribute('href').substring(1);
      const targetElement = document.getElementById(targetId);
      
      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth'
        });
      }
    });
  });
}

// Search functionality
function initSearchFunctionality() {
  const searchInput = document.getElementById('search-input');
  const searchResults = document.getElementById('search-results');
  
  if (searchInput) {
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
      clearTimeout(searchTimeout);
      const query = this.value.trim();
      
      if (query.length >= 3) {
        searchTimeout = setTimeout(() => {
          performSearch(query);
        }, 300);
      } else {
        if (searchResults) {
          searchResults.innerHTML = '';
          searchResults.classList.add('hidden');
        }
      }
    });
  }
}

function performSearch(query) {
  // This would typically make an AJAX request to a search endpoint
  // For now, we'll just show a placeholder
  const searchResults = document.getElementById('search-results');
  if (searchResults) {
    searchResults.innerHTML = `
      <div class="p-4 text-gray-500">
        Searching for "${query}"...
      </div>
    `;
    searchResults.classList.remove('hidden');
  }
}

// Admin-specific features
function initAdminFeatures() {
  initAjaxForms();
  initDataTables();
  initStatusToggles();
  initBulkActions();
}

// AJAX forms for admin actions
function initAjaxForms() {
  const ajaxForms = document.querySelectorAll('form[data-ajax]');
  
  ajaxForms.forEach(form => {
    form.addEventListener('submit', function(e) {
      e.preventDefault();
      
      const formData = new FormData(form);
      const url = form.action;
      const method = form.method || 'POST';
      
      fetch(url, {
        method: method,
        body: formData
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          showNotification('Success!', 'success');
          if (data.redirect) {
            window.location.href = data.redirect;
          }
        } else {
          showNotification(data.error || 'An error occurred', 'error');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred', 'error');
      });
    });
  });
}

// Data tables with sorting and filtering
function initDataTables() {
  const tables = document.querySelectorAll('table[data-sortable]');
  
  tables.forEach(table => {
    const headers = table.querySelectorAll('th[data-sort]');
    
    headers.forEach(header => {
      header.addEventListener('click', function() {
        const column = this.getAttribute('data-sort');
        sortTable(table, column);
      });
    });
  });
}

function sortTable(table, column) {
  // Basic table sorting implementation
  const tbody = table.querySelector('tbody');
  const rows = Array.from(tbody.querySelectorAll('tr'));
  
  rows.sort((a, b) => {
    const aValue = a.querySelector(`td[data-${column}]`).textContent.trim();
    const bValue = b.querySelector(`td[data-${column}]`).textContent.trim();
    
    return aValue.localeCompare(bValue);
  });
  
  rows.forEach(row => tbody.appendChild(row));
}

// Status toggles
function initStatusToggles() {
  const toggles = document.querySelectorAll('[data-toggle-status]');
  
  toggles.forEach(toggle => {
    toggle.addEventListener('click', function() {
      const url = this.getAttribute('data-url');
      
      fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          this.textContent = data.newStatus;
          showNotification('Status updated', 'success');
        } else {
          showNotification('Error updating status', 'error');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showNotification('Error updating status', 'error');
      });
    });
  });
}

// Bulk actions
function initBulkActions() {
  const selectAllCheckbox = document.getElementById('select-all');
  const itemCheckboxes = document.querySelectorAll('input[name="selected_items[]"]');
  const bulkActionButton = document.getElementById('bulk-action-button');
  
  if (selectAllCheckbox) {
    selectAllCheckbox.addEventListener('change', function() {
      itemCheckboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
      });
      updateBulkActionButton();
    });
  }
  
  itemCheckboxes.forEach(checkbox => {
    checkbox.addEventListener('change', updateBulkActionButton);
  });
  
  function updateBulkActionButton() {
    const selectedCount = document.querySelectorAll('input[name="selected_items[]"]:checked').length;
    if (bulkActionButton) {
      bulkActionButton.style.display = selectedCount > 0 ? 'block' : 'none';
      bulkActionButton.textContent = `Actions (${selectedCount} selected)`;
    }
  }
}

// Notification system
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${getNotificationClasses(type)}`;
  notification.textContent = message;
  
  document.body.appendChild(notification);
  
  // Auto-remove after 5 seconds
  setTimeout(() => {
    notification.remove();
  }, 5000);
  
  // Remove on click
  notification.addEventListener('click', () => {
    notification.remove();
  });
}

function getNotificationClasses(type) {
  switch (type) {
    case 'success':
      return 'bg-green-500 text-white';
    case 'error':
      return 'bg-red-500 text-white';
    case 'warning':
      return 'bg-yellow-500 text-white';
    default:
      return 'bg-blue-500 text-white';
  }
}

// Utility functions
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
