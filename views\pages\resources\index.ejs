<!-- Resources Index Page -->

<!-- Hero Section -->
<section class="bg-gradient-to-r from-green-600 to-teal-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">Research Resources</h1>
            <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
                Discover tools, papers, datasets, and educational materials to support your research
            </p>
        </div>
    </div>
</section>

<!-- Filter Section -->
<section class="py-8 bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
            <!-- Search Bar -->
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <input 
                        type="text" 
                        id="resource-search"
                        placeholder="Search resources..." 
                        class="form-input pl-10 w-full"
                    >
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <!-- Filter Options -->
            <div class="flex gap-4">
                <select id="category-filter" class="form-select">
                    <option value="">All Categories</option>
                    <option value="Research Papers">Research Papers</option>
                    <option value="Tools">Tools</option>
                    <option value="Datasets">Datasets</option>
                    <option value="Tutorials">Tutorials</option>
                    <option value="Software">Software</option>
                </select>
                
                <select id="type-filter" class="form-select">
                    <option value="">All Types</option>
                    <option value="pdf">PDF</option>
                    <option value="link">Link</option>
                    <option value="video">Video</option>
                </select>
            </div>
        </div>
    </div>
</section>

<!-- Featured Resources -->
<% if (resources && resources.some(resource => resource.featured)) { %>
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Featured Resources</h2>
            <div class="grid-3 gap-6">
                <% resources.filter(resource => resource.featured).slice(0, 3).forEach(resource => { %>
                    <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                                <% if (resource.type === 'pdf') { %>
                                    <i class="fas fa-file-pdf text-red-600 text-xl"></i>
                                <% } else if (resource.type === 'video') { %>
                                    <i class="fas fa-play-circle text-blue-600 text-xl"></i>
                                <% } else { %>
                                    <i class="fas fa-external-link-alt text-green-600 text-xl"></i>
                                <% } %>
                            </div>
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                                <i class="fas fa-star mr-1"></i>Featured
                            </span>
                        </div>
                        
                        <h3 class="text-xl font-semibold text-gray-900 mb-3"><%= resource.title %></h3>
                        <p class="text-gray-600 mb-4"><%= resource.description %></p>
                        
                        <div class="flex items-center justify-between mb-4">
                            <span class="bg-blue-100 text-blue-800 text-sm px-2 py-1 rounded-full">
                                <%= resource.category %>
                            </span>
                            <span class="resource-type bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full uppercase">
                                <%= resource.type %>
                            </span>
                        </div>
                        
                        <% if (resource.tags && resource.tags.length > 0) { %>
                            <div class="flex flex-wrap gap-1 mb-4">
                                <% resource.tags.slice(0, 3).forEach(tag => { %>
                                    <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                                        <%= tag %>
                                    </span>
                                <% }) %>
                            </div>
                        <% } %>
                        
                        <% if (resource.type === 'pdf' && resource.filePath) { %>
                            <a href="<%= resource.filePath %>" target="_blank" class="btn-primary w-full text-center">
                                <i class="fas fa-download mr-2"></i>Download PDF
                            </a>
                        <% } else if (resource.url) { %>
                            <a href="<%= resource.url %>" target="_blank" class="btn-primary w-full text-center">
                                <i class="fas fa-external-link-alt mr-2"></i>Access Resource
                            </a>
                        <% } %>
                    </div>
                <% }) %>
            </div>
        </div>
    </section>
<% } %>

<!-- All Resources -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">All Resources</h2>
        
        <% if (resources && resources.length > 0) { %>
            <div class="grid-3 gap-6" id="resources-grid">
                <% resources.forEach(resource => { %>
                    <div class="resource-card bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-300" 
                         data-category="<%= resource.category %>" 
                         data-type="<%= resource.type %>"
                         data-tags="<%= resource.tags ? resource.tags.join(',') : '' %>">
                        
                        <!-- Resource Icon -->
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 rounded-lg flex items-center justify-center mr-4
                                <% if (resource.type === 'pdf') { %>bg-red-100<% } else if (resource.type === 'video') { %>bg-blue-100<% } else { %>bg-green-100<% } %>">
                                <% if (resource.type === 'pdf') { %>
                                    <i class="fas fa-file-pdf text-red-600 text-xl"></i>
                                <% } else if (resource.type === 'video') { %>
                                    <i class="fas fa-play-circle text-blue-600 text-xl"></i>
                                <% } else { %>
                                    <i class="fas fa-external-link-alt text-green-600 text-xl"></i>
                                <% } %>
                            </div>
                            <% if (resource.featured) { %>
                                <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                                    <i class="fas fa-star mr-1"></i>Featured
                                </span>
                            <% } %>
                        </div>
                        
                        <h3 class="text-lg font-semibold text-gray-900 mb-2"><%= resource.title %></h3>
                        <p class="text-gray-600 mb-4 text-sm">
                            <%= resource.description.length > 120 ? resource.description.substring(0, 120) + '...' : resource.description %>
                        </p>
                        
                        <div class="flex items-center justify-between mb-4">
                            <span class="bg-blue-100 text-blue-800 text-sm px-2 py-1 rounded-full">
                                <%= resource.category %>
                            </span>
                            <span class="resource-type bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full uppercase">
                                <%= resource.type %>
                            </span>
                        </div>
                        
                        <% if (resource.tags && resource.tags.length > 0) { %>
                            <div class="flex flex-wrap gap-1 mb-4">
                                <% resource.tags.slice(0, 3).forEach(tag => { %>
                                    <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                                        <%= tag %>
                                    </span>
                                <% }) %>
                                <% if (resource.tags.length > 3) { %>
                                    <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                                        +<%= resource.tags.length - 3 %> more
                                    </span>
                                <% } %>
                            </div>
                        <% } %>
                        
                        <% if (resource.type === 'pdf' && resource.filePath) { %>
                            <a href="<%= resource.filePath %>" target="_blank" class="btn-primary w-full text-center">
                                <i class="fas fa-download mr-2"></i>Download PDF
                            </a>
                        <% } else if (resource.url) { %>
                            <a href="<%= resource.url %>" target="_blank" class="btn-primary w-full text-center">
                                <i class="fas fa-external-link-alt mr-2"></i>Access Resource
                            </a>
                        <% } %>
                    </div>
                <% }) %>
            </div>
        <% } else { %>
            <div class="text-center py-16">
                <div class="w-32 h-32 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-book text-gray-400 text-4xl"></i>
                </div>
                <h3 class="text-2xl font-semibold text-gray-900 mb-4">No Resources Found</h3>
                <p class="text-gray-600 mb-8">
                    We don't have any resources to display at the moment. 
                    Check back soon for helpful research materials!
                </p>
                <a href="/contact" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>Suggest a Resource
                </a>
            </div>
        <% } %>
    </div>
</section>

<!-- Call to Action -->
<section class="py-16 bg-green-600 text-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-4">Have a Useful Resource?</h2>
        <p class="text-xl mb-8 opacity-90">
            Share valuable research tools, papers, or educational materials with our community.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/contact" class="bg-white text-green-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors duration-200">
                <i class="fas fa-share mr-2"></i>Share Resource
            </a>
            <a href="/about" class="border-2 border-white text-white hover:bg-white hover:text-green-600 font-medium py-3 px-8 rounded-lg transition-all duration-200">
                <i class="fas fa-info-circle mr-2"></i>Learn More
            </a>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('resource-search');
    const categoryFilter = document.getElementById('category-filter');
    const typeFilter = document.getElementById('type-filter');
    const resourcesGrid = document.getElementById('resources-grid');
    const resourceCards = Array.from(document.querySelectorAll('.resource-card'));

    function filterResources() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedCategory = categoryFilter.value;
        const selectedType = typeFilter.value;

        let filteredResources = resourceCards.filter(card => {
            const title = card.querySelector('h3').textContent.toLowerCase();
            const description = card.querySelector('p').textContent.toLowerCase();
            const category = card.getAttribute('data-category');
            const type = card.getAttribute('data-type');
            const tags = card.getAttribute('data-tags').toLowerCase();
            
            const matchesSearch = title.includes(searchTerm) || description.includes(searchTerm) || tags.includes(searchTerm);
            const matchesCategory = !selectedCategory || category === selectedCategory;
            const matchesType = !selectedType || type === selectedType;
            
            return matchesSearch && matchesCategory && matchesType;
        });

        // Hide all cards
        resourceCards.forEach(card => card.style.display = 'none');

        // Show filtered cards
        filteredResources.forEach(card => card.style.display = 'block');

        // Show no results message if needed
        if (filteredResources.length === 0) {
            if (!document.getElementById('no-results')) {
                const noResults = document.createElement('div');
                noResults.id = 'no-results';
                noResults.className = 'col-span-full text-center py-16';
                noResults.innerHTML = `
                    <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-search text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No Resources Found</h3>
                    <p class="text-gray-600">Try adjusting your search criteria or filters.</p>
                `;
                resourcesGrid.appendChild(noResults);
            }
        } else {
            const noResults = document.getElementById('no-results');
            if (noResults) {
                noResults.remove();
            }
        }
    }

    // Event listeners
    searchInput.addEventListener('input', filterResources);
    categoryFilter.addEventListener('change', filterResources);
    typeFilter.addEventListener('change', filterResources);
});
</script>
