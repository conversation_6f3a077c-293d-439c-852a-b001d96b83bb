const { Event } = require('../models');
const path = require('path');
const fs = require('fs');
const multer = require('multer');

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'public/uploads/events');
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    // Accept only images
    const filetypes = /jpeg|jpg|png|gif|webp/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    
    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('Only image files are allowed!'));
  }
});

// Middleware for handling file uploads
exports.uploadImage = upload.single('image');

// Get all events
exports.getAllEvents = async (req, res) => {
  try {
    const currentDate = new Date();
    
    // Get upcoming events
    const upcomingEvents = await Event.find({ 
      date: { $gte: currentDate },
      isPast: false
    }).sort({ date: 1 });
    
    // Get past events
    const pastEvents = await Event.find({ 
      $or: [
        { date: { $lt: currentDate } },
        { isPast: true }
      ]
    }).sort({ date: -1 });
    
    res.render('pages/events/index', {
      title: 'Science Club - Events',
      upcomingEvents,
      pastEvents
    });
  } catch (error) {
    console.error('Error fetching events:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching events.' 
    });
  }
};

// Get a single event by ID
exports.getEventById = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    
    if (!event) {
      return res.status(404).render('pages/404', { 
        title: '404 - Event Not Found' 
      });
    }
    
    res.render('pages/events/show', {
      title: `Science Club - ${event.title}`,
      event
    });
  } catch (error) {
    console.error('Error fetching event:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching the event.' 
    });
  }
};

// Admin: Get all events
exports.getAdminEvents = async (req, res) => {
  try {
    const events = await Event.find().sort({ date: -1 });
    
    res.render('admin/events/index', {
      title: 'Admin - Events',
      events
    });
  } catch (error) {
    console.error('Error fetching admin events:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching events.' 
    });
  }
};

// Admin: Get form to create a new event
exports.getCreateEventForm = (req, res) => {
  res.render('admin/events/create', {
    title: 'Admin - Create New Event'
  });
};

// Admin: Create a new event
exports.createEvent = async (req, res) => {
  try {
    const { title, description, date, endDate, location, organizer, registrationLink, featured } = req.body;
    
    // Create new event
    const newEvent = new Event({
      title,
      description,
      date: new Date(date),
      endDate: endDate ? new Date(endDate) : null,
      location,
      organizer: organizer || 'Science Club',
      registrationLink: registrationLink || null,
      featured: featured === 'on',
      image: req.file ? `/uploads/events/${req.file.filename}` : null
    });
    
    await newEvent.save();
    
    res.redirect('/admin/events');
  } catch (error) {
    console.error('Error creating event:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while creating the event.' 
    });
  }
};

// Admin: Get form to edit an event
exports.getEditEventForm = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    
    if (!event) {
      return res.status(404).render('pages/404', { 
        title: '404 - Event Not Found' 
      });
    }
    
    res.render('admin/events/edit', {
      title: `Admin - Edit Event: ${event.title}`,
      event
    });
  } catch (error) {
    console.error('Error fetching event for edit:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching the event for editing.' 
    });
  }
};

// Admin: Update an event
exports.updateEvent = async (req, res) => {
  try {
    const { title, description, date, endDate, location, organizer, registrationLink, featured } = req.body;
    
    // Find the event
    const event = await Event.findById(req.params.id);
    
    if (!event) {
      return res.status(404).render('pages/404', { 
        title: '404 - Event Not Found' 
      });
    }
    
    // Update event fields
    event.title = title;
    event.description = description;
    event.date = new Date(date);
    event.endDate = endDate ? new Date(endDate) : null;
    event.location = location;
    event.organizer = organizer || 'Science Club';
    event.registrationLink = registrationLink || null;
    event.featured = featured === 'on';
    
    // Update image if a new one was uploaded
    if (req.file) {
      // Delete old image if it exists
      if (event.image) {
        const oldImagePath = path.join(__dirname, '../public', event.image);
        if (fs.existsSync(oldImagePath)) {
          fs.unlinkSync(oldImagePath);
        }
      }
      
      event.image = `/uploads/events/${req.file.filename}`;
    }
    
    await event.save();
    
    res.redirect('/admin/events');
  } catch (error) {
    console.error('Error updating event:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while updating the event.' 
    });
  }
};

// Admin: Delete an event
exports.deleteEvent = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    
    if (!event) {
      return res.status(404).render('pages/404', { 
        title: '404 - Event Not Found' 
      });
    }
    
    // Delete associated image file if it exists
    if (event.image) {
      const imagePath = path.join(__dirname, '../public', event.image);
      if (fs.existsSync(imagePath)) {
        fs.unlinkSync(imagePath);
      }
    }
    
    await Event.findByIdAndDelete(req.params.id);
    
    res.redirect('/admin/events');
  } catch (error) {
    console.error('Error deleting event:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while deleting the event.' 
    });
  }
};
