<!-- Blog Index Page -->

<!-- Hero Section -->
<section class="bg-gradient-to-r from-green-600 to-blue-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">Science Blog</h1>
            <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
                Stay updated with the latest discoveries, research findings, and scientific breakthroughs
            </p>
        </div>
    </div>
</section>

<!-- Filter and Search Section -->
<section class="py-8 bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
            <!-- Search Bar -->
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <input 
                        type="text" 
                        id="blog-search"
                        placeholder="Search articles..." 
                        class="form-input pl-10 w-full"
                    >
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <!-- Filter Options -->
            <div class="flex gap-4">
                <select id="category-filter" class="form-select">
                    <option value="">All Categories</option>
                    <option value="Research">Research</option>
                    <option value="Technology">Technology</option>
                    <option value="Medicine">Medicine</option>
                    <option value="Environment">Environment</option>
                    <option value="Physics">Physics</option>
                    <option value="Biology">Biology</option>
                </select>
                
                <select id="sort-filter" class="form-select">
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="title">Title A-Z</option>
                    <option value="featured">Featured First</option>
                </select>
            </div>
        </div>
    </div>
</section>

<!-- Featured Posts Section -->
<% if (blogs && blogs.some(blog => blog.featured)) { %>
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Featured Articles</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <% blogs.filter(blog => blog.featured).slice(0, 2).forEach(blog => { %>
                    <article class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <% if (blog.image) { %>
                            <img src="<%= blog.image %>" alt="<%= blog.title %>" class="w-full h-64 object-cover">
                        <% } else { %>
                            <div class="w-full h-64 bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center">
                                <i class="fas fa-newspaper text-white text-4xl"></i>
                            </div>
                        <% } %>
                        
                        <div class="p-8">
                            <div class="flex items-center mb-4">
                                <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full mr-3">
                                    <i class="fas fa-star mr-1"></i>Featured
                                </span>
                                <span class="text-gray-500 text-sm">
                                    <%= new Date(blog.createdAt).toLocaleDateString() %>
                                </span>
                            </div>
                            
                            <h3 class="text-2xl font-bold text-gray-900 mb-3">
                                <a href="/blog/<%= blog.slug %>" class="hover:text-blue-600 transition-colors">
                                    <%= blog.title %>
                                </a>
                            </h3>
                            
                            <p class="text-gray-600 mb-4"><%= blog.shortDescription %></p>
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <% if (blog.author) { %>
                                        <span class="text-sm text-gray-500">
                                            <i class="fas fa-user mr-1"></i>
                                            <%= blog.author.name %>
                                        </span>
                                    <% } %>
                                </div>
                                
                                <a href="/blog/<%= blog.slug %>" class="btn-primary">
                                    Read More <i class="fas fa-arrow-right ml-1"></i>
                                </a>
                            </div>
                        </div>
                    </article>
                <% }) %>
            </div>
        </div>
    </section>
<% } %>

<!-- All Posts Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Latest Articles</h2>
        
        <% if (blogs && blogs.length > 0) { %>
            <div class="grid-3" id="blog-grid">
                <% blogs.forEach(blog => { %>
                    <article class="blog-card" data-categories="<%= blog.categories ? blog.categories.join(',') : '' %>">
                        <% if (blog.image) { %>
                            <img src="<%= blog.image %>" alt="<%= blog.title %>" class="blog-image">
                        <% } else { %>
                            <div class="w-full h-48 bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center">
                                <i class="fas fa-newspaper text-white text-3xl"></i>
                            </div>
                        <% } %>
                        
                        <div class="blog-content">
                            <% if (blog.featured) { %>
                                <div class="mb-2">
                                    <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                                        <i class="fas fa-star mr-1"></i>Featured
                                    </span>
                                </div>
                            <% } %>
                            
                            <div class="blog-meta">
                                <i class="fas fa-calendar mr-1"></i>
                                <%= new Date(blog.createdAt).toLocaleDateString() %>
                                <% if (blog.author) { %>
                                    | <i class="fas fa-user ml-2 mr-1"></i><%= blog.author.name %>
                                <% } %>
                            </div>
                            
                            <h3 class="blog-title">
                                <a href="/blog/<%= blog.slug %>" class="hover:text-blue-600 transition-colors">
                                    <%= blog.title %>
                                </a>
                            </h3>
                            
                            <p class="blog-excerpt"><%= blog.shortDescription %></p>
                            
                            <% if (blog.categories && blog.categories.length > 0) { %>
                                <div class="blog-categories">
                                    <% blog.categories.slice(0, 3).forEach(category => { %>
                                        <span class="blog-category"><%= category %></span>
                                    <% }) %>
                                </div>
                            <% } %>
                            
                            <div class="mt-4">
                                <a href="/blog/<%= blog.slug %>" class="btn-primary">
                                    <i class="fas fa-arrow-right mr-1"></i>Read More
                                </a>
                            </div>
                        </div>
                    </article>
                <% }) %>
            </div>
        <% } else { %>
            <div class="text-center py-16">
                <div class="w-32 h-32 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-newspaper text-gray-400 text-4xl"></i>
                </div>
                <h3 class="text-2xl font-semibold text-gray-900 mb-4">No Articles Found</h3>
                <p class="text-gray-600 mb-8">
                    We don't have any blog posts to display at the moment. 
                    Check back soon for the latest science news and insights!
                </p>
                <a href="/contact" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>Suggest a Topic
                </a>
            </div>
        <% } %>
    </div>
</section>

<!-- Newsletter Signup -->
<section class="py-16 bg-blue-600 text-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-4">Stay Updated</h2>
        <p class="text-xl mb-8 opacity-90">
            Subscribe to our newsletter and never miss the latest scientific discoveries and research insights.
        </p>
        <form class="max-w-md mx-auto flex flex-col sm:flex-row gap-4">
            <input 
                type="email" 
                placeholder="Enter your email" 
                class="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-300"
                required
            >
            <button type="submit" class="bg-white text-blue-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors duration-200">
                <i class="fas fa-paper-plane mr-2"></i>Subscribe
            </button>
        </form>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('blog-search');
    const categoryFilter = document.getElementById('category-filter');
    const sortFilter = document.getElementById('sort-filter');
    const blogGrid = document.getElementById('blog-grid');
    const blogCards = Array.from(document.querySelectorAll('.blog-card'));

    function filterAndSortBlogs() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedCategory = categoryFilter.value;
        const sortBy = sortFilter.value;

        // Filter blogs
        let filteredBlogs = blogCards.filter(card => {
            const title = card.querySelector('.blog-title a').textContent.toLowerCase();
            const excerpt = card.querySelector('.blog-excerpt').textContent.toLowerCase();
            const categories = card.getAttribute('data-categories').toLowerCase();
            
            const matchesSearch = title.includes(searchTerm) || excerpt.includes(searchTerm);
            const matchesCategory = !selectedCategory || categories.includes(selectedCategory.toLowerCase());
            
            return matchesSearch && matchesCategory;
        });

        // Sort blogs
        filteredBlogs.sort((a, b) => {
            switch (sortBy) {
                case 'title':
                    return a.querySelector('.blog-title a').textContent.localeCompare(b.querySelector('.blog-title a').textContent);
                case 'featured':
                    const aFeatured = a.querySelector('.bg-yellow-100') ? 1 : 0;
                    const bFeatured = b.querySelector('.bg-yellow-100') ? 1 : 0;
                    return bFeatured - aFeatured;
                case 'oldest':
                    return Array.prototype.indexOf.call(blogCards, a) - Array.prototype.indexOf.call(blogCards, b);
                default: // newest
                    return Array.prototype.indexOf.call(blogCards, b) - Array.prototype.indexOf.call(blogCards, a);
            }
        });

        // Hide all cards
        blogCards.forEach(card => card.style.display = 'none');

        // Show filtered and sorted cards
        filteredBlogs.forEach(card => card.style.display = 'block');

        // Show no results message if needed
        if (filteredBlogs.length === 0) {
            if (!document.getElementById('no-results')) {
                const noResults = document.createElement('div');
                noResults.id = 'no-results';
                noResults.className = 'col-span-full text-center py-16';
                noResults.innerHTML = `
                    <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-search text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No Articles Found</h3>
                    <p class="text-gray-600">Try adjusting your search criteria or filters.</p>
                `;
                blogGrid.appendChild(noResults);
            }
        } else {
            const noResults = document.getElementById('no-results');
            if (noResults) {
                noResults.remove();
            }
        }
    }

    // Event listeners
    searchInput.addEventListener('input', filterAndSortBlogs);
    categoryFilter.addEventListener('change', filterAndSortBlogs);
    sortFilter.addEventListener('change', filterAndSortBlogs);
});
</script>
