<!-- Home Page -->

<!-- Hero Section -->
<section class="hero">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div class="text-center">
            <h1 class="hero-title">
                Advancing Science Through
                <span class="text-blue-300">Student Research</span>
            </h1>
            <p class="hero-subtitle">
                Join our community of passionate researchers, innovators, and science enthusiasts.
                Discover groundbreaking projects, share knowledge, and shape the future of science.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/projects" class="btn-primary text-lg px-8 py-3">
                    <i class="fas fa-flask mr-2"></i>Explore Projects
                </a>
                <a href="/contact" class="btn-outline text-lg px-8 py-3 border-white text-white hover:bg-white hover:text-blue-600">
                    <i class="fas fa-users mr-2"></i>Join Us
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
                <div class="text-3xl font-bold text-blue-600 mb-2">50+</div>
                <div class="text-gray-600">Active Projects</div>
            </div>
            <div>
                <div class="text-3xl font-bold text-blue-600 mb-2">200+</div>
                <div class="text-gray-600">Members</div>
            </div>
            <div>
                <div class="text-3xl font-bold text-blue-600 mb-2">15+</div>
                <div class="text-gray-600">Research Areas</div>
            </div>
            <div>
                <div class="text-3xl font-bold text-blue-600 mb-2">100+</div>
                <div class="text-gray-600">Publications</div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Projects Section -->
<section class="section bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="section-title">Featured Research Projects</h2>
            <p class="section-subtitle">
                Discover innovative research projects led by our talented student researchers
            </p>
        </div>

        <% if (featuredProjects && featuredProjects.length > 0) { %>
            <div class="grid-3">
                <% featuredProjects.forEach(project => { %>
                    <div class="project-card">
                        <% if (project.image) { %>
                            <img src="<%= project.image %>" alt="<%= project.title %>" class="project-image">
                        <% } else { %>
                            <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                                <i class="fas fa-flask text-white text-4xl"></i>
                            </div>
                        <% } %>
                        <div class="project-content">
                            <h3 class="project-title"><%= project.title %></h3>
                            <p class="project-description"><%= project.description.substring(0, 150) %>...</p>
                            <% if (project.authors && project.authors.length > 0) { %>
                                <p class="project-authors">
                                    <i class="fas fa-user mr-1"></i>
                                    <%= project.authors.join(', ') %>
                                </p>
                            <% } %>
                            <% if (project.tags && project.tags.length > 0) { %>
                                <div class="project-tags">
                                    <% project.tags.slice(0, 3).forEach(tag => { %>
                                        <span class="project-tag"><%= tag %></span>
                                    <% }) %>
                                </div>
                            <% } %>
                            <div class="mt-4">
                                <a href="/projects/<%= project._id %>" class="btn-primary">
                                    <i class="fas fa-arrow-right mr-1"></i>Learn More
                                </a>
                            </div>
                        </div>
                    </div>
                <% }) %>
            </div>
        <% } else { %>
            <div class="text-center py-12">
                <i class="fas fa-flask text-gray-400 text-6xl mb-4"></i>
                <p class="text-gray-500 text-lg">No featured projects available at the moment.</p>
            </div>
        <% } %>

        <div class="text-center mt-12">
            <a href="/projects" class="btn-outline">
                <i class="fas fa-th-large mr-2"></i>View All Projects
            </a>
        </div>
    </div>
</section>

<!-- Latest Blog Posts Section -->
<section class="section bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="section-title">Latest Science News</h2>
            <p class="section-subtitle">
                Stay updated with the latest discoveries, research findings, and scientific breakthroughs
            </p>
        </div>

        <% if (latestBlogs && latestBlogs.length > 0) { %>
            <div class="grid-3">
                <% latestBlogs.forEach(blog => { %>
                    <article class="blog-card">
                        <% if (blog.image) { %>
                            <img src="<%= blog.image %>" alt="<%= blog.title %>" class="blog-image">
                        <% } else { %>
                            <div class="w-full h-48 bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center">
                                <i class="fas fa-newspaper text-white text-4xl"></i>
                            </div>
                        <% } %>
                        <div class="blog-content">
                            <div class="blog-meta">
                                <i class="fas fa-calendar mr-1"></i>
                                <%= new Date(blog.createdAt).toLocaleDateString() %>
                                <% if (blog.author) { %>
                                    | <i class="fas fa-user ml-2 mr-1"></i><%= blog.author.name %>
                                <% } %>
                            </div>
                            <h3 class="blog-title"><%= blog.title %></h3>
                            <p class="blog-excerpt"><%= blog.shortDescription %></p>
                            <% if (blog.categories && blog.categories.length > 0) { %>
                                <div class="blog-categories">
                                    <% blog.categories.slice(0, 2).forEach(category => { %>
                                        <span class="blog-category"><%= category %></span>
                                    <% }) %>
                                </div>
                            <% } %>
                            <div class="mt-4">
                                <a href="/blog/<%= blog.slug %>" class="btn-primary">
                                    <i class="fas fa-arrow-right mr-1"></i>Read More
                                </a>
                            </div>
                        </div>
                    </article>
                <% }) %>
            </div>
        <% } else { %>
            <div class="text-center py-12">
                <i class="fas fa-newspaper text-gray-400 text-6xl mb-4"></i>
                <p class="text-gray-500 text-lg">No blog posts available at the moment.</p>
            </div>
        <% } %>

        <div class="text-center mt-12">
            <a href="/blog" class="btn-outline">
                <i class="fas fa-newspaper mr-2"></i>View All Posts
            </a>
        </div>
    </div>
</section>

<!-- Upcoming Events Section -->
<section class="section bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="section-title">Upcoming Events</h2>
            <p class="section-subtitle">
                Join us for workshops, seminars, conferences, and networking events
            </p>
        </div>

        <% if (upcomingEvents && upcomingEvents.length > 0) { %>
            <div class="grid-3">
                <% upcomingEvents.forEach(event => { %>
                    <div class="event-card">
                        <div class="event-date">
                            <div class="text-sm font-medium">
                                <%= new Date(event.date).toLocaleDateString('en-US', { month: 'short' }) %>
                            </div>
                            <div class="text-2xl font-bold">
                                <%= new Date(event.date).getDate() %>
                            </div>
                        </div>
                        <div class="event-content">
                            <h3 class="event-title"><%= event.title %></h3>
                            <p class="event-description"><%= event.description.substring(0, 100) %>...</p>
                            <div class="event-location">
                                <i class="fas fa-map-marker-alt mr-1"></i>
                                <%= event.location %>
                            </div>
                            <div class="mt-4">
                                <a href="/events/<%= event._id %>" class="btn-primary">
                                    <i class="fas fa-info-circle mr-1"></i>Learn More
                                </a>
                            </div>
                        </div>
                    </div>
                <% }) %>
            </div>
        <% } else { %>
            <div class="text-center py-12">
                <i class="fas fa-calendar text-gray-400 text-6xl mb-4"></i>
                <p class="text-gray-500 text-lg">No upcoming events at the moment.</p>
            </div>
        <% } %>

        <div class="text-center mt-12">
            <a href="/events" class="btn-outline">
                <i class="fas fa-calendar mr-2"></i>View All Events
            </a>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="py-16 bg-blue-600 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">Ready to Join Our Community?</h2>
        <p class="text-xl mb-8 opacity-90">
            Connect with fellow researchers, access exclusive resources, and contribute to groundbreaking discoveries.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/contact" class="bg-white text-blue-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors duration-200">
                <i class="fas fa-user-plus mr-2"></i>Become a Member
            </a>
            <a href="/projects" class="border-2 border-white text-white hover:bg-white hover:text-blue-600 font-medium py-3 px-8 rounded-lg transition-all duration-200">
                <i class="fas fa-rocket mr-2"></i>Start a Project
            </a>
        </div>
    </div>
</section>
