const { Project } = require('../models');
const path = require('path');
const fs = require('fs');
const multer = require('multer');

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'public/uploads/projects');
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    if (file.fieldname === 'image') {
      // Accept only images
      const filetypes = /jpeg|jpg|png|gif/;
      const mimetype = filetypes.test(file.mimetype);
      const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
      
      if (mimetype && extname) {
        return cb(null, true);
      }
      cb(new Error('Only image files are allowed!'));
    } else if (file.fieldname === 'pdfFile') {
      // Accept only PDFs
      const filetypes = /pdf/;
      const mimetype = filetypes.test(file.mimetype);
      const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
      
      if (mimetype && extname) {
        return cb(null, true);
      }
      cb(new Error('Only PDF files are allowed!'));
    } else {
      cb(new Error('Unexpected field'));
    }
  }
});

// Middleware for handling file uploads
exports.uploadFiles = upload.fields([
  { name: 'image', maxCount: 1 },
  { name: 'pdfFile', maxCount: 1 }
]);

// Get all projects
exports.getAllProjects = async (req, res) => {
  try {
    const projects = await Project.find().sort({ createdAt: -1 });
    
    res.render('pages/projects/index', {
      title: 'Science Club - Research Projects',
      projects
    });
  } catch (error) {
    console.error('Error fetching projects:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching projects.' 
    });
  }
};

// Get a single project by ID
exports.getProjectById = async (req, res) => {
  try {
    const project = await Project.findById(req.params.id);
    
    if (!project) {
      return res.status(404).render('pages/404', { 
        title: '404 - Project Not Found' 
      });
    }
    
    res.render('pages/projects/show', {
      title: `Science Club - ${project.title}`,
      project
    });
  } catch (error) {
    console.error('Error fetching project:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching the project.' 
    });
  }
};

// Admin: Get form to create a new project
exports.getCreateProjectForm = (req, res) => {
  res.render('admin/projects/create', {
    title: 'Admin - Create New Project'
  });
};

// Admin: Create a new project
exports.createProject = async (req, res) => {
  try {
    const { title, description, authors, tags, featured } = req.body;
    
    // Process authors and tags (convert comma-separated string to array)
    const authorsArray = authors.split(',').map(author => author.trim());
    const tagsArray = tags ? tags.split(',').map(tag => tag.trim()) : [];
    
    // Create new project
    const newProject = new Project({
      title,
      description,
      authors: authorsArray,
      tags: tagsArray,
      featured: featured === 'on',
      image: req.files.image ? `/uploads/projects/${req.files.image[0].filename}` : null,
      pdfFile: req.files.pdfFile ? `/uploads/projects/${req.files.pdfFile[0].filename}` : null
    });
    
    await newProject.save();
    
    res.redirect('/admin/projects');
  } catch (error) {
    console.error('Error creating project:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while creating the project.' 
    });
  }
};

// Admin: Get form to edit a project
exports.getEditProjectForm = async (req, res) => {
  try {
    const project = await Project.findById(req.params.id);
    
    if (!project) {
      return res.status(404).render('pages/404', { 
        title: '404 - Project Not Found' 
      });
    }
    
    res.render('admin/projects/edit', {
      title: `Admin - Edit Project: ${project.title}`,
      project
    });
  } catch (error) {
    console.error('Error fetching project for edit:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while fetching the project for editing.' 
    });
  }
};

// Admin: Update a project
exports.updateProject = async (req, res) => {
  try {
    const { title, description, authors, tags, featured } = req.body;
    
    // Process authors and tags (convert comma-separated string to array)
    const authorsArray = authors.split(',').map(author => author.trim());
    const tagsArray = tags ? tags.split(',').map(tag => tag.trim()) : [];
    
    // Find the project
    const project = await Project.findById(req.params.id);
    
    if (!project) {
      return res.status(404).render('pages/404', { 
        title: '404 - Project Not Found' 
      });
    }
    
    // Update project fields
    project.title = title;
    project.description = description;
    project.authors = authorsArray;
    project.tags = tagsArray;
    project.featured = featured === 'on';
    
    // Update image if a new one was uploaded
    if (req.files.image) {
      // Delete old image if it exists
      if (project.image) {
        const oldImagePath = path.join(__dirname, '../public', project.image);
        if (fs.existsSync(oldImagePath)) {
          fs.unlinkSync(oldImagePath);
        }
      }
      
      project.image = `/uploads/projects/${req.files.image[0].filename}`;
    }
    
    // Update PDF if a new one was uploaded
    if (req.files.pdfFile) {
      // Delete old PDF if it exists
      if (project.pdfFile) {
        const oldPdfPath = path.join(__dirname, '../public', project.pdfFile);
        if (fs.existsSync(oldPdfPath)) {
          fs.unlinkSync(oldPdfPath);
        }
      }
      
      project.pdfFile = `/uploads/projects/${req.files.pdfFile[0].filename}`;
    }
    
    await project.save();
    
    res.redirect('/admin/projects');
  } catch (error) {
    console.error('Error updating project:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while updating the project.' 
    });
  }
};

// Admin: Delete a project
exports.deleteProject = async (req, res) => {
  try {
    const project = await Project.findById(req.params.id);
    
    if (!project) {
      return res.status(404).json({ success: false, message: 'Project not found' });
    }
    
    // Delete associated files
    if (project.image) {
      const imagePath = path.join(__dirname, '../public', project.image);
      if (fs.existsSync(imagePath)) {
        fs.unlinkSync(imagePath);
      }
    }
    
    if (project.pdfFile) {
      const pdfPath = path.join(__dirname, '../public', project.pdfFile);
      if (fs.existsSync(pdfPath)) {
        fs.unlinkSync(pdfPath);
      }
    }
    
    await Project.findByIdAndDelete(req.params.id);
    
    res.redirect('/admin/projects');
  } catch (error) {
    console.error('Error deleting project:', error);
    res.status(500).render('pages/error', { 
      title: 'Error', 
      error: 'An error occurred while deleting the project.' 
    });
  }
};
