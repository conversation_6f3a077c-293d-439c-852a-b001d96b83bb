<!-- Project Detail Page -->

<!-- Hero Section -->
<section class="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl">
            <% if (project.featured) { %>
                <div class="mb-4">
                    <span class="bg-yellow-400 text-yellow-900 text-sm px-3 py-1 rounded-full font-medium">
                        <i class="fas fa-star mr-1"></i>Featured Project
                    </span>
                </div>
            <% } %>
            
            <h1 class="text-4xl md:text-5xl font-bold mb-6"><%= project.title %></h1>
            
            <div class="flex flex-wrap items-center gap-6 text-lg opacity-90">
                <% if (project.authors && project.authors.length > 0) { %>
                    <div class="flex items-center">
                        <i class="fas fa-user mr-2"></i>
                        <span><%= project.authors.join(', ') %></span>
                    </div>
                <% } %>
                
                <div class="flex items-center">
                    <i class="fas fa-calendar mr-2"></i>
                    <span><%= new Date(project.createdAt).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }) %></span>
                </div>
                
                <% if (project.updatedAt && project.updatedAt !== project.createdAt) { %>
                    <div class="flex items-center">
                        <i class="fas fa-edit mr-2"></i>
                        <span>Updated <%= new Date(project.updatedAt).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }) %></span>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</section>

<!-- Main Content -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Project Image -->
                <% if (project.image) { %>
                    <div class="mb-8">
                        <img 
                            src="<%= project.image %>" 
                            alt="<%= project.title %>" 
                            class="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
                        >
                    </div>
                <% } %>
                
                <!-- Project Description -->
                <div class="prose prose-lg max-w-none">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">Project Overview</h2>
                    <div class="text-gray-700 leading-relaxed whitespace-pre-line"><%= project.description %></div>
                </div>
                
                <!-- Tags -->
                <% if (project.tags && project.tags.length > 0) { %>
                    <div class="mt-8">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Research Areas</h3>
                        <div class="flex flex-wrap gap-2">
                            <% project.tags.forEach(tag => { %>
                                <span class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
                                    <%= tag %>
                                </span>
                            <% }) %>
                        </div>
                    </div>
                <% } %>
                
                <!-- Share Section -->
                <div class="mt-12 pt-8 border-t border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Share This Project</h3>
                    <div class="flex space-x-4">
                        <a href="#" onclick="shareOnTwitter()" class="flex items-center px-4 py-2 bg-blue-400 text-white rounded-lg hover:bg-blue-500 transition-colors">
                            <i class="fab fa-twitter mr-2"></i>Twitter
                        </a>
                        <a href="#" onclick="shareOnLinkedIn()" class="flex items-center px-4 py-2 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors">
                            <i class="fab fa-linkedin-in mr-2"></i>LinkedIn
                        </a>
                        <a href="#" onclick="shareOnFacebook()" class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fab fa-facebook-f mr-2"></i>Facebook
                        </a>
                        <button onclick="copyToClipboard()" class="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                            <i class="fas fa-link mr-2"></i>Copy Link
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <div class="sticky top-8 space-y-6">
                    <!-- Project Actions -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Project Resources</h3>
                        <div class="space-y-3">
                            <% if (project.pdfFile) { %>
                                <a href="<%= project.pdfFile %>" target="_blank" class="btn-primary w-full text-center">
                                    <i class="fas fa-file-pdf mr-2"></i>Download PDF
                                </a>
                            <% } %>
                            
                            <a href="/contact" class="btn-outline w-full text-center">
                                <i class="fas fa-envelope mr-2"></i>Contact Authors
                            </a>
                            
                            <a href="/projects" class="btn-secondary w-full text-center">
                                <i class="fas fa-arrow-left mr-2"></i>Back to Projects
                            </a>
                        </div>
                    </div>
                    
                    <!-- Project Stats -->
                    <div class="bg-blue-50 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Project Details</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Created:</span>
                                <span class="font-medium"><%= new Date(project.createdAt).toLocaleDateString() %></span>
                            </div>
                            
                            <% if (project.updatedAt && project.updatedAt !== project.createdAt) { %>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Last Updated:</span>
                                    <span class="font-medium"><%= new Date(project.updatedAt).toLocaleDateString() %></span>
                                </div>
                            <% } %>
                            
                            <% if (project.authors && project.authors.length > 0) { %>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Researchers:</span>
                                    <span class="font-medium"><%= project.authors.length %></span>
                                </div>
                            <% } %>
                            
                            <% if (project.tags && project.tags.length > 0) { %>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Research Areas:</span>
                                    <span class="font-medium"><%= project.tags.length %></span>
                                </div>
                            <% } %>
                        </div>
                    </div>
                    
                    <!-- Related Projects -->
                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Related Projects</h3>
                        <div class="space-y-4">
                            <!-- This would be populated with related projects based on tags -->
                            <div class="text-gray-500 text-sm text-center py-4">
                                Related projects will be shown here based on research areas and tags.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">Interested in This Research?</h2>
        <p class="text-lg text-gray-600 mb-8">
            Get in touch with the research team or explore more projects in similar areas.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/contact" class="btn-primary text-lg px-8 py-3">
                <i class="fas fa-envelope mr-2"></i>Contact Researchers
            </a>
            <a href="/projects" class="btn-outline text-lg px-8 py-3">
                <i class="fas fa-search mr-2"></i>Explore More Projects
            </a>
        </div>
    </div>
</section>

<script>
function shareOnTwitter() {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent('Check out this research project: <%= project.title %>');
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
}

function shareOnLinkedIn() {
    const url = encodeURIComponent(window.location.href);
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank');
}

function shareOnFacebook() {
    const url = encodeURIComponent(window.location.href);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
}

function copyToClipboard() {
    navigator.clipboard.writeText(window.location.href).then(function() {
        // Show success message
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check mr-2"></i>Copied!';
        button.classList.add('bg-green-600', 'hover:bg-green-700');
        button.classList.remove('bg-gray-600', 'hover:bg-gray-700');
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('bg-green-600', 'hover:bg-green-700');
            button.classList.add('bg-gray-600', 'hover:bg-gray-700');
        }, 2000);
    });
}
</script>
